name: Release

on:
  push:
    branches:
      - main # or whatever branch you want to use

jobs:
  release:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check for changes in package.json
        run: |
          git diff --cached --diff-filter=d package.json || {
            echo "No changes to package.json"
            exit 1
          }

      - name: Get version number from package.json
        id: get_version
        run: |
          VERSION=$(jq -r '.version' package.json)
          echo "::set-output name=version::$VERSION"

      - name: Extract latest CHANGELOG entry
        id: changelog
        run: |
          CHANGELOG_CONTENT=$(awk 'BEGIN {print_section=0;} /^## \[/ {if (print_section == 0) {print_section=1;} else {exit;}} print_section {print;}' CHANGELOG.md)
          CHANGELOG_ESCAPED=$(echo "$CHANGELOG_CONTENT" | sed ':a;N;$!ba;s/\n/%0A/g')
          echo "Extracted latest release notes from CHANGELOG.md:" 
          echo -e "$CHANGELOG_CONTENT" 
          echo "::set-output name=content::$CHANGELOG_ESCAPED"

      - name: Create GitHub release
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const changelog = `${{ steps.changelog.outputs.content }}`;
            const release = await github.rest.repos.createRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag_name: `v${{ steps.get_version.outputs.version }}`,
              name: `v${{ steps.get_version.outputs.version }}`,
              body: changelog,
            })
            console.log(`Created release ${release.data.html_url}`)

      - name: Upload package to GitHub release
        uses: actions/upload-artifact@v4
        with:
          name: package
          path: |
            .
            !.git
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Trigger Docker build workflow
        uses: actions/github-script@v7
        with:
          script: |
            github.rest.actions.createWorkflowDispatch({
              owner: context.repo.owner,
              repo: context.repo.repo,
              workflow_id: 'docker-build.yaml',
              ref: 'v${{ steps.get_version.outputs.version }}',
            })
