import { test, expect } from '@playwright/test';

// Configure test with longer timeout
test.setTimeout(120000);

// Simple logging wrapper to prevent duplicates
let logCount = 0;
const log = (/** @type {string} */ message) => {
  logCount++;
  console.log(`[${logCount}] ${message}`);
};

test('Complete auth flow: signin/signup and signout', async ({ page }) => {
  log('🚀 Starting complete auth flow test...');

  // Enable console logging from the page (simplified)
  page.on('console', msg => {
    // Only log important messages to reduce noise
    const text = msg.text();
    if (text.includes('Backend config') || text.includes('connected') || text.includes('user') || text.includes('error')) {
      log(`PAGE: ${text}`);
    }
  });

  page.on('pageerror', error => log(`PAGE ERROR: ${error.message}`));

  // Navigate to the auth page
  log('📍 Navigating to auth page...');
  await page.goto('http://localhost:5173/auth?redirect=%2F', {
    waitUntil: 'networkidle',
    timeout: 30000
  });
  log(`✅ Initial navigation complete. URL: ${page.url()}`);

  await page.waitForTimeout(2000);

  // Take initial screenshot
  await page.screenshot({ path: 'debug-01-initial-load.png' });
  log('📷 Initial screenshot saved');

  // Try to sign in with existing credentials first
  log('🔐 Attempting to sign in with existing credentials...');
  const signInSuccessful = await attemptSignIn(page);

  if (!signInSuccessful) {
    log('📝 Sign in failed, attempting signup...');
    // If sign in failed, try signup process
    await performSignUp(page);
  } else {
    log('✅ Sign in successful!');
  }

  // Verify we're logged in
  log('🔍 Verifying logged in state...');
  await verifyLoggedIn(page);
  log('✅ Login verification completed!');

  // Now sign out as part of the test flow
  log('🔓 Starting sign out process...');
  await performSignOut(page);
  log('🎉 Complete auth flow test completed successfully!');
});

/**
 * Performs sign out as part of the main test flow
 * @param {import("@playwright/test").Page} page
 */
async function performSignOut(page) {
  log('🔍 Looking for user profile button to sign out...');

  // Take screenshot before attempting signout
  await page.screenshot({ path: 'debug-05-before-signout.png' });

  // Use the stable test ID selector (find the visible one since there might be multiple instances)
  const profileButton = page.locator('[data-testid="user-menu-button"]').locator('visible=true').first();

  // Verify the profile button exists and is visible
  await expect(profileButton).toBeVisible({ timeout: 10000 });
  log('✅ Profile button is visible');

  // Click the profile button
  await profileButton.click();
  await page.waitForTimeout(1000);
  log('✅ Profile button clicked, menu should be open');

  // Take screenshot after clicking profile button
  await page.screenshot({ path: 'debug-06-profile-menu-open.png' });

  // Look for and click the sign out button
  const signOutButton = page.locator('div[role="menuitem"]:has-text("Sign Out")');
  await expect(signOutButton).toBeVisible({ timeout: 5000 });
  log('✅ Sign out button is visible');

  await signOutButton.click();
  log('✅ Sign out button clicked');

  // Wait for redirect and verify we're back on the auth page
  await page.waitForTimeout(3000);

  const currentUrl = page.url();
  log(`🔍 URL after sign out: ${currentUrl}`);

  // Take screenshot after sign out
  await page.screenshot({ path: 'debug-07-after-signout.png' });

  // Verify we're redirected to the auth page
  const isOnAuthPage = currentUrl.includes('/auth') || currentUrl === 'http://localhost:5173/auth';
  expect(isOnAuthPage).toBe(true);
  log('✅ Successfully signed out and redirected to auth page');
}

/**
 * Validates that the login form is properly displayed on the auth page
 * @param {import("@playwright/test").Page} page
 * @param {string} screenshotSuffix - Suffix for screenshot filename
 * @returns {Promise<boolean>} - Returns true if login form is present, false if signup form is shown
 */
async function validateLoginFormPresent(page, screenshotSuffix = 'form-validation') {
  log('🔍 Validating login form presence...');

  // Take screenshot for debugging
  await page.screenshot({ path: `debug-${screenshotSuffix}.png` });

  // Check if we're on the signup page (no admin exists yet)
  const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
  const getStartedCount = await getStartedButton.count();

  if (getStartedCount > 0) {
    log('📝 Signup form detected - no admin exists yet');
    return false;
  }

  // Validate login form elements are present and visible
  const emailField = page.locator('#email');
  const passwordField = page.locator('#password');
  const signinButton = page.locator('button:has-text("Sign in")');

  // Check element counts
  const emailCount = await emailField.count();
  const passwordCount = await passwordField.count();
  const signinCount = await signinButton.count();

  log(`🔍 Form elements - Email: ${emailCount}, Password: ${passwordCount}, Sign in button: ${signinCount}`);

  // Verify all required elements are present
  if (emailCount === 0 || passwordCount === 0 || signinCount === 0) {
    log('❌ Login form elements missing');
    return false;
  }

  // Verify elements are visible
  try {
    await expect(emailField).toBeVisible({ timeout: 5000 });
    await expect(passwordField).toBeVisible({ timeout: 5000 });
    await expect(signinButton).toBeVisible({ timeout: 5000 });
    log('✅ All login form elements are present and visible');
    return true;
  } catch (error) {
    log(`❌ Login form elements not visible: ${error.message}`);
    return false;
  }
}

async function attemptSignIn(page) {
  try {
    log('🔍 Checking page state for sign in...');
    await page.screenshot({ path: 'debug-04-signin-attempt.png' });

    // Check if we're on the signup page (no admin exists yet)
    const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
    const getStartedCount = await getStartedButton.count();

    if (getStartedCount > 0) {
      log('📝 No admin exists yet, need to signup first');
      return false;
    }

    // Try to sign in with known credentials
    const emailField = page.locator('#email');
    const passwordField = page.locator('#password');
    const signinButton = page.locator('button:has-text("Sign in")');

    const emailCount = await emailField.count();
    const passwordCount = await passwordField.count();
    const signinCount = await signinButton.count();

    log(`🔍 Form elements - Email: ${emailCount}, Password: ${passwordCount}, Sign in button: ${signinCount}`);

    if (emailCount > 0 && passwordCount > 0 && signinCount > 0) {
      log('📝 Filling sign in form...');
      await emailField.fill('<EMAIL>');
      await passwordField.fill('testpass123');

      await page.screenshot({ path: 'debug-05-signin-form-filled.png' });

      await signinButton.click();
      log('✅ Sign in button clicked, waiting for response...');
      await page.waitForTimeout(3000);

      const currentUrl = page.url();
      log(`🔍 URL after sign in attempt: ${currentUrl}`);

      // Check if signin was successful (redirected away from auth page)
      if (!currentUrl.includes('/auth') || currentUrl === 'http://localhost:5173/') {
        log('✅ Sign in appears successful - redirected away from auth page');
        await page.screenshot({ path: 'debug-06-signin-success.png' });
        return true;
      } else {
        log('❌ Sign in failed - still on auth page');
        await page.screenshot({ path: 'debug-07-signin-failed.png' });
      }
    } else {
      log('❌ Sign in form elements not found');
    }

    return false;
  } catch (error) {
    log(`❌ Error during sign in attempt: ${error.message}`);
    await page.screenshot({ path: 'debug-08-signin-error.png' });
    return false;
  }
}

async function performSignUp(page) {
  log('📝 Starting signup process...');

  // Click Get Started to access signup form
  const getStartedButton = page.locator('button[aria-labelledby="get-started"]');
  const getStartedCount = await getStartedButton.count();

  if (getStartedCount > 0) {
    await getStartedButton.click();
    log('✅ Get Started button clicked');
    await page.waitForTimeout(2000);
    await page.screenshot({ path: 'debug-09-signup-form.png' });
  } else {
    log('❌ Get Started button not found');
    return;
  }

  // Fill out admin signup form
  log('📝 Filling signup form...');
  await page.locator('#name').fill('Test Admin');
  await page.locator('#email').fill('<EMAIL>');
  await page.locator('#password').fill('testpass123');

  await page.screenshot({ path: 'debug-10-signup-form-filled.png' });

  // Submit signup form
  const createButton = page.locator('button:has-text("Create Admin Account")');
  const createButtonCount = await createButton.count();

  if (createButtonCount > 0) {
    await createButton.click();
    log('✅ Create Admin Account button clicked');
    await page.waitForTimeout(3000);

    const urlAfterSignup = page.url();
    log(`🔍 URL after signup: ${urlAfterSignup}`);
    await page.screenshot({ path: 'debug-11-after-signup.png' });
  }

  // Click the "Okay, Let's Go!" button if it appears
  const okayLetsGoButton = page.locator('button:has-text("Okay, Let\'s Go!")');
  const okayButtonCount = await okayLetsGoButton.count();

  if (okayButtonCount > 0) {
    await okayLetsGoButton.click();
    log('✅ "Okay, Let\'s Go!" button clicked');
    await page.waitForTimeout(2000);

    const finalUrl = page.url();
    log(`🔍 Final URL after signup flow: ${finalUrl}`);
    await page.screenshot({ path: 'debug-12-signup-complete.png' });
  }
}

async function verifyLoggedIn(page) {
  log('🔍 Starting verification of logged in state...');

  const currentUrl = page.url();
  log(`🔍 Current URL: ${currentUrl}`);

  // Take screenshot for verification
  await page.screenshot({ path: 'debug-03-verification-start.png' });

  // Check for signs that we're logged in (not on auth page)
  const loggedIn = !currentUrl.includes('/auth');
  log(`🔍 URL check - logged in: ${loggedIn}`);

  if (!loggedIn) {
    log('❌ Still on auth page, login may have failed');
  }

  expect(loggedIn).toBe(true);

  // Verify user profile button is present using the stable test ID
  log('🔍 Looking for user profile button...');
  const profileButton = page.locator('[data-testid="user-menu-button"]').locator('visible=true').first();

  await expect(profileButton).toBeVisible({ timeout: 10000 });
  log('✅ Profile button found and visible');

  await page.screenshot({ path: 'debug-04-verification-success.png' });
  log('✅ Login verification successful!');
}