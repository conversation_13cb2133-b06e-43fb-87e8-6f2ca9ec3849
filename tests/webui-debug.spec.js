import { test } from '@playwright/test';

test('Debug Open WebUI network requests', async ({ page }) => {
  console.log('📍 Navigating to http://localhost:5173/auth?redirect=%2F');
  
  await page.goto('http://localhost:5173/auth?redirect=%2F', { 
    waitUntil: 'networkidle',
    timeout: 30000 
  });
  
  // Wait a bit more to capture any delayed requests
  await page.waitForTimeout(5000);
  
  // Check if we're on the error page and why
  if ((await page.url()).includes('/error')) {
    console.log('✅ Debug test completed - error page detected');
  } else {
    console.log('✅ Debug test completed - page loaded successfully');
  }
});
