#!/bin/bash

# Open WebUI E2E Service Stopper
# Safely stops all E2E services and resets configuration

echo "=== Stopping Open WebUI E2E Services ==="

# Stop services using stored PIDs (most reliable method)
if [ -f "backend.pid" ]; then
    BACKEND_PID=$(cat backend.pid)
    if kill -0 "$BACKEND_PID" 2>/dev/null; then
        echo "Stopping backend (PID: $BACKEND_PID)..."
        kill -TERM "$BACKEND_PID" 2>/dev/null || true
        sleep 2
        kill -KILL "$BACKEND_PID" 2>/dev/null || true
    fi
    rm -f backend.pid
fi

if [ -f "frontend.pid" ]; then
    FRONTEND_PID=$(cat frontend.pid)
    if kill -0 "$FRONTEND_PID" 2>/dev/null; then
        echo "Stopping frontend (PID: $FRONTEND_PID)..."
        kill -TERM "$FRONTEND_PID" 2>/dev/null || true
        sleep 2
        kill -KILL "$FRONTEND_PID" 2>/dev/null || true
    fi
    rm -f frontend.pid
fi

# Fallback cleanup using process patterns
echo "Performing fallback cleanup..."
pkill -f "uvicorn.*open_webui" 2>/dev/null || true
pkill -f "vite.*dev" 2>/dev/null || true
pkill -f "npm.*dev" 2>/dev/null || true

# Force cleanup of frontend ports
for port in 5173 5174 5175; do
    lsof -ti:$port | xargs -r kill -9 2>/dev/null || true
done

# Clean up database for fresh E2E tests
echo "Cleaning up database..."
if [ -f "backend/data/webui.db" ]; then
    rm -f backend/data/webui.db
    echo "• Database file removed"
else
    echo "• No database file found"
fi

echo ""
echo "=== Services Stopped Successfully ==="
echo "• All processes terminated"
echo "• Ports 5173-5175 freed"
echo "• Database cleaned for fresh tests"
echo "• Ready for next Playwright test startup"
echo ""
