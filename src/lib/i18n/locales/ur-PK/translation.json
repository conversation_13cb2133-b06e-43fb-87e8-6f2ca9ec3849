{"-1 for no limit, or a positive integer for a specific limit": "", "'s', 'm', 'h', 'd', 'w' or '-1' for no expiration.": "'s', 'm', 'h', 'd', 'w' یا '1-' مستقل کے لیے", "(e.g. `sh webui.sh --api --api-auth username_password`)": "(مثال کے طور پر: `sh webui.sh --api --api-auth username_password`)", "(e.g. `sh webui.sh --api`)": "(مثال کے طور پر: `sh webui.sh --api`)", "(latest)": "(تازہ ترین)", "(leave blank for to use commercial endpoint)": "", "[Last] dddd [at] h:mm A": "", "[Today at] h:mm A": "", "[Yesterday at] h:mm A": "", "{{ models }}": "{{ ماڈلز }}", "{{COUNT}} Available Tools": "", "{{COUNT}} characters": "", "{{COUNT}} hidden lines": "", "{{COUNT}} Replies": "", "{{COUNT}} words": "", "{{user}}'s Chats": "{{ صارف }} کی بات چیت", "{{webUIName}} Backend Required": "{{webUIName}} بیک اینڈ درکار ہے", "*Prompt node ID(s) are required for image generation": "تصویر کی تخلیق کے لیے *پرومپٹ نوڈ آئی ڈی(ز) کی ضرورت ہے", "A new version (v{{LATEST_VERSION}}) is now available.": "نیا ورژن (v{{LATEST_VERSION}}) اب دستیاب ہے", "A task model is used when performing tasks such as generating titles for chats and web search queries": "ٹاسک ماڈل اس وقت استعمال ہوتا ہے جب چیٹس کے عنوانات اور ویب سرچ سوالات تیار کیے جا رہے ہوں", "a user": "ایک صارف", "About": "بارے میں", "Accept autocomplete generation / Jump to prompt variable": "", "Access": "", "Access Control": "", "Accessible to all users": "", "Account": "اکاؤنٹ", "Account Activation Pending": "اکاؤنٹ فعال ہونے کا انتظار ہے", "Accurate information": "درست معلومات", "Action": "", "Actions": "اعمال", "Activate": "", "Activate this command by typing \"/{{COMMAND}}\" to chat input.": "", "Active Users": "فعال صارفین", "Add": "شامل", "Add a model ID": "", "Add a short description about what this model does": "اس ماڈل کے کام کے بارے میں ایک مختصر وضاحت شامل کریں", "Add a tag": "ٹیگ شامل کریں", "Add Arena Model": "ارینا ماڈل شامل کریں", "Add Connection": "", "Add Content": "مواد شامل کریں", "Add content here": "یہاں مواد شامل کریں", "Add Custom Parameter": "", "Add custom prompt": "حسب ضرورت پرامپٹ شامل کریں", "Add Files": "فائلیں شامل کریں", "Add Group": "", "Add Memory": "میموری شامل کریں", "Add Model": "ماڈل شامل کریں", "Add Reaction": "", "Add Tag": "ٹیگ شامل کریں", "Add Tags": "ٹیگز شامل کریں", "Add text content": "متن کا مواد شامل کریں", "Add User": "صارف شامل کریں", "Add User Group": "", "Adjusting these settings will apply changes universally to all users.": "ان سیٹنگز کو ایڈجسٹ کرنے سے تمام صارفین کے لئے تبدیلیاں یکساں طور پر نافذ ہوں گی", "admin": "ایڈمن", "Admin": "ایڈمن", "Admin Panel": "ایڈمن پینل", "Admin Settings": "ایڈمن ترتیبات", "Admins have access to all tools at all times; users need tools assigned per model in the workspace.": "ایڈمنز کو ہر وقت تمام ٹولز تک رسائی حاصل ہوتی ہے؛ صارفین کو ورک سپیس میں ماڈل کے حساب سے ٹولز تفویض کرنے کی ضرورت ہوتی ہے", "Advanced Parameters": "پیشرفتہ پیرا میٹرز", "Advanced Params": "ترقی یافتہ پیرامیٹرز", "AI": "", "All": "", "All Documents": "تمام دستاویزات", "All models deleted successfully": "", "Allow Call": "", "Allow Chat Controls": "", "Allow Chat Delete": "", "Allow Chat Deletion": "چیٹ کو حذف کرنے کی اجازت دیں", "Allow Chat Edit": "", "Allow Chat Export": "", "Allow Chat Share": "", "Allow Chat System Prompt": "", "Allow File Upload": "", "Allow Multiple Models in Chat": "", "Allow non-local voices": "غیر مقامی آوازوں کی اجازت دیں", "Allow Speech to Text": "", "Allow Temporary Chat": "عارضی چیٹ کی اجازت دیں", "Allow Text to Speech": "", "Allow User Location": "صارف کی مقام کی اجازت دیں", "Allow Voice Interruption in Call": "کال میں آواز کی مداخلت کی اجازت دیں", "Allowed Endpoints": "", "Allowed File Extensions": "", "Allowed file extensions for upload. Separate multiple extensions with commas. Leave empty for all file types.": "", "Already have an account?": "کیا پہلے سے اکاؤنٹ موجود ہے؟", "Alternative to the top_p, and aims to ensure a balance of quality and variety. The parameter p represents the minimum probability for a token to be considered, relative to the probability of the most likely token. For example, with p=0.05 and the most likely token having a probability of 0.9, logits with a value less than 0.045 are filtered out.": "", "Always": "", "Always Collapse Code Blocks": "", "Always Expand Details": "", "Always Play Notification Sound": "", "Amazing": "", "an assistant": "معاون", "Analyzed": "", "Analyzing...": "", "and": "اور", "and {{COUNT}} more": "اور {{COUNT}} مزید", "and create a new shared link.": "اور ایک نیا مشترکہ لنک بنائیں", "Android": "", "API": "", "API Base URL": "API بنیادی URL", "API details for using a vision-language model in the picture description. This parameter is mutually exclusive with picture_description_local.": "", "API Key": "اے پی آئی کلید", "API Key created.": "اے پی آئی کلید بنائی گئی", "API Key Endpoint Restrictions": "", "API keys": "API کیز", "API Version": "", "Application DN": "", "Application DN Password": "", "applies to all users with the \"user\" role": "", "April": "اپریل", "Archive": "آرکائیو", "Archive All Chats": "تمام چیٹس محفوظ کریں", "Archived Chats": "محفوظ شدہ بات چیت", "archived-chat-export": "", "Are you sure you want to clear all memories? This action cannot be undone.": "", "Are you sure you want to delete this channel?": "", "Are you sure you want to delete this message?": "", "Are you sure you want to unarchive all archived chats?": "", "Are you sure?": "کیا آپ کو یقین ہے؟", "Arena Models": "ارینا ماڈلز", "Artifacts": "نوادرات", "Ask": "", "Ask a question": "سوال پوچھیں", "Assistant": "اسسٹنٹ", "Attach file from knowledge": "", "Attention to detail": "تفصیل پر توجہ", "Attribute for Mail": "", "Attribute for Username": "", "Audio": "آڈیو", "August": "اگست", "Auth": "", "Authenticate": "", "Authentication": "", "Auto": "", "Auto-Copy Response to Clipboard": "جواب خودکار طور پر کلپ بورڈ پر کاپی ہو گیا", "Auto-playback response": "آٹو پلے بیک جواب", "Autocomplete Generation": "", "Autocomplete Generation Input Max Length": "", "Automatic1111": "آٹو میٹک1111", "AUTOMATIC1111 Api Auth String": "AUTOMATIC1111 ایپلی کیشن کا تصدیقی سلسلہ", "AUTOMATIC1111 Base URL": "AUTOMATIC1111 بنیادی URL", "AUTOMATIC1111 Base URL is required.": "AUTOMATIC1111 بنیادی URL درکار ہے", "Available list": "دستیاب فہرست", "Available Tools": "", "available!": "دستیاب!", "Awful": "", "Azure AI Speech": "ایژور اے آئی اسپیچ", "Azure Region": "ایزور ریجن", "Back": "واپس", "Bad Response": "<PERSON><PERSON><PERSON> جواب", "Banners": "بی<PERSON><PERSON><PERSON>", "Base Model (From)": "بیس ماڈل (سے)", "Base Model List Cache speeds up access by fetching base models only at startup or on settings save—faster, but may not show recent base model changes.": "", "before": "پہلے", "Being lazy": "سستی کر رہا ہے", "Beta": "", "Bing Search V7 Endpoint": "", "Bing Search V7 Subscription Key": "", "Bocha Search API Key": "", "Bold": "", "Boosting or penalizing specific tokens for constrained responses. Bias values will be clamped between -100 and 100 (inclusive). (Default: none)": "", "Both Docling OCR Engine and Language(s) must be provided or both left empty.": "", "Brave Search API Key": "بریو سرچ API کلید", "Bullet List": "", "By {{name}}": "", "Bypass Embedding and Retrieval": "", "Bypass Web Loader": "", "Cache Base Model List": "", "Calendar": "", "Call": "کال کریں", "Call feature is not supported when using Web STT engine": "کال کی خصوصیت ویب STT انجن استعمال کرتے وقت معاونت یافتہ نہیں ہے", "Camera": "کیمرہ", "Cancel": "منسوخ کریں", "Capabilities": "صلاحیتیں", "Capture": "", "Capture Audio": "", "Certificate Path": "", "Change Password": "پاس ورڈ تبدیل کریں", "Channel Name": "", "Channels": "", "Character": "کردار", "Character limit for autocomplete generation input": "", "Chart new frontiers": "", "Chat": "چیٹ", "Chat Background Image": "چیٹ پس منظر کی تصویر", "Chat Bubble UI": "چیٹ بلبل انٹرفیس", "Chat Controls": "چیٹ کنٹرولز", "Chat direction": "چیٹ کی سمت", "Chat Overview": "چیٹ کا جائزہ", "Chat Permissions": "", "Chat Tags Auto-Generation": "چیٹ ٹیگز خودکار تخلیق", "Chats": "بات چیت", "Check Again": "دوبارہ چیک کریں", "Check for updates": "تازہ ترین معلومات کے لیے چیک کریں", "Checking for updates...": "تازہ ترین معلومات کی جانچ کر رہا ہے...", "Choose a model before saving...": "محفوظ کرنے سے پہلے ایک ماڈل منتخب کریں...", "Chunk Overlap": "حصے کی اوورلیپ", "Chunk Size": "چنک سائز", "Ciphers": "", "Citation": "حوا<PERSON>ہ", "Citations": "", "Clear memory": "میموری صاف کریں", "Clear Memory": "", "click here": "", "Click here for filter guides.": "", "Click here for help.": "مدد کے لیے یہاں کلک کریں", "Click here to": "یہاں کلک کریں تاکہ", "Click here to download user import template file.": "صارف امپورٹ ٹیمپلیٹ فائل ڈاؤن لوڈ کرنے کے لیے یہاں کلک کریں", "Click here to learn more about faster-whisper and see the available models.": "تیز ویسپر کے بارے میں مزید جاننے اور دستیاب ماڈلز دیکھنے کے لیے یہاں کلک کریں", "Click here to see available models.": "", "Click here to select": "منتخب کرنے کے لیے یہاں کلک کریں", "Click here to select a csv file.": "یہاں کلک کریں تاکہ CSV فائل منتخب کریں", "Click here to select a py file.": "یہاں کلک کریں تاکہ پی وائی فائل منتخب کریں", "Click here to upload a workflow.json file.": "یہاں کلک کریں تاکہ ورک فلو.json فائل اپ لوڈ کریں", "click here.": "یہاں کلک کریں", "Click on the user role button to change a user's role.": "صارف کا کردار تبدیل کرنے کے لیے صارف کردار بٹن پر کلک کریں", "Clipboard write permission denied. Please check your browser settings to grant the necessary access.": "کلپ بورڈ لکھنے کی اجازت نہیں دی گئی براہ کرم ضروری رسائی کی اجازت دینے کے لیے اپنے براؤزر کی سیٹنگز چیک کریں", "Clone": "نقل کریں", "Clone Chat": "", "Clone of {{TITLE}}": "", "Close": "بند کریں", "Close Configure Connection Modal": "", "Close modal": "", "Close settings modal": "", "Code Block": "", "Code execution": "کوڈ کا نفاذ", "Code Execution": "", "Code Execution Engine": "", "Code Execution Timeout": "", "Code formatted successfully": "کوڈ کامیابی سے فارمیٹ ہو گیا", "Code Interpreter": "", "Code Interpreter Engine": "", "Code Interpreter Prompt Template": "", "Collapse": "", "Collection": "کلیکشن", "Color": "", "ComfyUI": "کومفی یو آئی", "ComfyUI API Key": "", "ComfyUI Base URL": "کمفی یو آئی بیس یو آر ایل", "ComfyUI Base URL is required.": "ComfyUI بیس یو آر ایل ضروری ہے", "ComfyUI Workflow": "کومفی یو آئی ورک فلو", "ComfyUI Workflow Nodes": "کومفی یو آئی ورک فلو نوڈز", "Command": "کمانڈ", "Comment": "", "Completions": "تکمیل", "Concurrent Requests": "ہم وقت درخواستیں", "Configure": "", "Confirm": "تصدیق کریں", "Confirm Password": "پاس ورڈ کی توثیق کریں", "Confirm your action": "اپنی کارروائی کی تصدیق کریں", "Confirm your new password": "", "Connect to your own OpenAI compatible API endpoints.": "", "Connect to your own OpenAPI compatible external tool servers.": "", "Connection failed": "", "Connection successful": "", "Connection Type": "", "Connections": "کنکشنز", "Connections saved successfully": "", "Connections settings updated": "", "Constrains effort on reasoning for reasoning models. Only applicable to reasoning models from specific providers that support reasoning effort.": "", "Contact Admin for WebUI Access": "ویب یو آئی رسائی کے لیے ایڈمن سے رابطہ کریں", "Content": "مواد", "Content Extraction Engine": "", "Continue Response": "ردع<PERSON>ل جاری رکھیں", "Continue with {{provider}}": "{{provider}} کے ساتھ جاری رکھیں", "Continue with Email": "", "Continue with LDAP": "", "Control how message text is split for TTS requests. 'Punctuation' splits into sentences, 'paragraphs' splits into paragraphs, and 'none' keeps the message as a single string.": "TTS درخواستوں کے لیے پیغام کے متن کی تقسیم کو کنٹرول کریں 'Punctuation' جملوں میں تقسیم کرتا ہے، 'paragraphs' پیراگراف میں تقسیم کرتا ہے، اور 'none' پیغام کو ایک ہی سٹرنگ کے طور پر رکھتا ہے", "Control the repetition of token sequences in the generated text. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 1.1) will be more lenient. At 1, it is disabled.": "", "Controls": "کنٹرو<PERSON>ز", "Controls the balance between coherence and diversity of the output. A lower value will result in more focused and coherent text.": "", "Copied": "کاپی کیا گیا", "Copied link to clipboard": "", "Copied shared chat URL to clipboard!": "مشترکہ چیٹ یو آر ایل کلپ بورڈ میں نقل کر دیا گیا!", "Copied to clipboard": "کلپ بورڈ پر نقل کر دیا گیا", "Copy": "نقل کریں", "Copy Formatted Text": "", "Copy last code block": "آخری کوڈ بلاک نقل کریں", "Copy last response": "آخری جواب کاپی کریں", "Copy link": "", "Copy Link": "لنک کاپی کریں", "Copy to clipboard": "کلپ بورڈ پر کاپی کریں", "Copying to clipboard was successful!": "کلپ بورڈ میں کاپی کامیاب ہوئی!", "CORS must be properly configured by the provider to allow requests from Open WebUI.": "", "Create": "", "Create a knowledge base": "", "Create a model": "ماڈل بنائیں", "Create Account": "اکاؤنٹ بنائیں", "Create Admin Account": "", "Create Channel": "", "Create Folder": "", "Create Group": "", "Create Knowledge": "علم بنائیں", "Create new key": "نیا کلید بنائیں", "Create new secret key": "نیا خفیہ کلید بنائیں", "Create Note": "", "Create your first note by clicking on the plus button below.": "", "Created at": "پر بنایا گیا", "Created At": "بنایا گیا:", "Created by": "تخلیق کردہ", "CSV Import": "CSV درآمد کریں", "Ctrl+Enter to Send": "", "Current Model": "موجودہ ماڈل", "Current Password": "موجودہ پاس ورڈ", "Custom": "حسب ضرورت", "Custom description enabled": "", "Custom Parameter Name": "", "Custom Parameter Value": "", "Danger Zone": "", "Dark": "ڈارک", "Database": "ڈیٹا بیس", "Datalab Marker API": "", "Datalab Marker API Key required.": "", "DD/MM/YYYY": "", "December": "دسمبر", "Default": "پہلے سے طے شدہ", "Default (Open AI)": "ڈیفالٹ (اوپن اے آئی)", "Default (SentenceTransformers)": "ڈیفالٹ (سینٹینس ٹرانسفارمرز)", "Default description enabled": "", "Default mode works with a wider range of models by calling tools once before execution. Native mode leverages the model's built-in tool-calling capabilities, but requires the model to inherently support this feature.": "", "Default Model": "ڈیفالٹ ماڈل", "Default model updated": "ڈیفالٹ ماڈل اپ ڈیٹ ہو گیا", "Default Models": "", "Default permissions": "", "Default permissions updated successfully": "", "Default Prompt Suggestions": "ڈیفالٹ پرامپٹ تجاویز", "Default to 389 or 636 if TLS is enabled": "", "Default to ALL": "", "Default to segmented retrieval for focused and relevant content extraction, this is recommended for most cases.": "", "Default User Role": "ڈیفالٹ صارف کا کردار", "Delete": "حذف کریں", "Delete a model": "ایک ماڈل حذف کریں", "Delete All Chats": "تمام چیٹس حذف کریں", "Delete All Models": "", "Delete chat": "چیٹ حذف کریں", "Delete Chat": "چیٹ حذف کریں", "Delete chat?": "چیٹ حذف کریں؟", "Delete folder?": "کیا فولڈر حذف کریں؟", "Delete function?": "حذف کریں؟", "Delete Message": "", "Delete message?": "", "Delete note?": "", "Delete prompt?": "پرومپٹ کو حذف کریں؟", "delete this link": "اس لنک کو حذف کریں", "Delete tool?": "کیا آپ حذف کرنا چاہتے ہیں؟", "Delete User": "صارف کو حذف کریں", "Deleted {{deleteModelTag}}": "{{deleteModelTag}} حذف کر دیا گیا", "Deleted {{name}}": "حذف کر دیا گیا {{name}}", "Deleted User": "", "Deployment names are required for Azure OpenAI": "", "Describe Pictures in Documents": "", "Describe your knowledge base and objectives": "", "Description": "تفصیل", "Detect Artifacts Automatically": "", "Dictate": "", "Didn't fully follow instructions": "ہدایات کو مکمل طور پر نہیں سمجھا", "Direct": "", "Direct Connections": "", "Direct Connections allow users to connect to their own OpenAI compatible API endpoints.": "", "Direct Tool Servers": "", "Disable Code Interpreter": "", "Disable Image Extraction": "", "Disable image extraction from the PDF. If Use LLM is enabled, images will be automatically captioned. Defaults to False.": "", "Disabled": "غیر فعال", "Discover a function": "ایک فنکشن دریافت کریں", "Discover a model": "ایک ماڈل دریافت کریں", "Discover a prompt": "ایک اشارہ دریافت کریں", "Discover a tool": "ایک ٹول دریافت کریں", "Discover how to use Open WebUI and seek support from the community.": "", "Discover wonders": "", "Discover, download, and explore custom functions": "دریافت کریں، ڈاؤن لوڈ کریں، اور کسٹم فنکشنز کو دریافت کریں", "Discover, download, and explore custom prompts": "دریافت کریں، ڈاؤن لوڈ کریں، اور حسب ضرورت پرامپٹس کو دریافت کریں", "Discover, download, and explore custom tools": "دریافت کریں، ڈاؤن لوڈ کریں، اور حسب ضرورت ٹولز کو دریافت کریں", "Discover, download, and explore model presets": "دریافت کریں، ڈاؤن لوڈ کریں، اور ماڈل پریسیٹس کو دریافت کریں", "Display": "", "Display Emoji in Call": "کال میں ایموجی دکھائیں", "Display the username instead of You in the Chat": "چیٹ میں \"آپ\" کے بجائے صارف نام دکھائیں", "Displays citations in the response": "", "Dive into knowledge": "", "Do not install functions from sources you do not fully trust.": "ایسی جگہوں سے فنکشنز انسٹال نہ کریں جن پر آپ مکمل بھروسہ نہیں کرتے", "Do not install tools from sources you do not fully trust.": "جن ذرائع پر آپ مکمل بھروسہ نہیں کرتے، ان سے ٹولز انسٹال نہ کریں", "Docling": "", "Docling Server URL required.": "", "Document": "دستاویز", "Document Intelligence": "", "Document Intelligence endpoint and key required.": "", "Documentation": "دستاویزات", "Documents": "دستاویزات", "does not make any external connections, and your data stays securely on your locally hosted server.": "آپ کا ڈیٹا مقامی طور پر میزبانی شدہ سرور پر محفوظ رہتا ہے اور کوئی بیرونی رابطے نہیں بناتا", "Domain Filter List": "", "Don't have an account?": "کیا آپ کے پاس اکاؤنٹ نہیں ہے؟", "don't install random functions from sources you don't trust.": "غیر معتبر ذرائع سے بے ترتیب فنکشنز انسٹال نہ کریں", "don't install random tools from sources you don't trust.": "جو ذرائع آپ پر بھروسہ نہیں کرتے ان سے بے ترتیب ٹولز انسٹال نہ کریں", "Don't like the style": "انداز پسند نہیں آیا", "Done": "ہو گیا", "Download": "ڈاؤن لوڈ کریں", "Download as SVG": "", "Download canceled": "ڈاؤن لوڈ منسوخ کر دیا گیا", "Download Database": "ڈیٹا بیس ڈاؤن لوڈ کریں", "Drag and drop a file to upload or select a file to view": "", "Draw": "ڈرائنگ کریں", "Drop any files here to upload": "", "e.g. '30s','10m'. Valid time units are 's', 'm', 'h'.": "مثلاً '30s'، '10m' درست وقت کی اکائیاں ہیں 's'، 'm'، 'h'", "e.g. \"json\" or a JSON schema": "", "e.g. 60": "", "e.g. A filter to remove profanity from text": "", "e.g. en": "", "e.g. My Filter": "", "e.g. My Tools": "", "e.g. my_filter": "", "e.g. my_tools": "", "e.g. pdf, docx, txt": "", "e.g. Tools for performing various operations": "", "e.g., 3, 4, 5 (leave blank for default)": "", "e.g., audio/wav,audio/mpeg,video/* (leave blank for defaults)": "", "e.g., en-US,ja-JP (leave blank for auto-detect)": "", "e.g., westus (leave blank for eastus)": "", "e.g.) en,fr,de": "", "Edit": "ترمیم کریں", "Edit Arena Model": "ایرینا ماڈل میں ترمیم کریں", "Edit Channel": "", "Edit Connection": "", "Edit Default Permissions": "", "Edit Folder": "", "Edit Memory": "یادداشت میں ترمیم کریں", "Edit User": "صارف میں ترمیم کریں", "Edit User Group": "", "Edited": "", "Editing": "", "Eject": "", "ElevenLabs": "الیون لیبز", "Email": "ای میل", "Embark on adventures": "", "Embedding": "", "Embedding Batch Size": "بیچ سائز شامل کرنا", "Embedding Model": "ایمبیڈنگ ماڈل", "Embedding Model Engine": "ایمبیڈنگ ماڈل انجن", "Embedding model set to \"{{embedding_model}}\"": "ایمبیڈنگ ماڈل \"{{embedding_model}}\" پر سیٹ کیا گیا ہے", "Enable API Key": "", "Enable autocomplete generation for chat messages": "", "Enable Code Execution": "", "Enable Code Interpreter": "", "Enable Community Sharing": "کمیونٹی شیئرنگ فعال کریں", "Enable Memory Locking (mlock) to prevent model data from being swapped out of RAM. This option locks the model's working set of pages into RAM, ensuring that they will not be swapped out to disk. This can help maintain performance by avoiding page faults and ensuring fast data access.": "", "Enable Memory Mapping (mmap) to load model data. This option allows the system to use disk storage as an extension of RAM by treating disk files as if they were in RAM. This can improve model performance by allowing for faster data access. However, it may not work correctly with all systems and can consume a significant amount of disk space.": "", "Enable Message Rating": "پیغام کی درجہ بندی فعال کریں", "Enable Mirostat sampling for controlling perplexity.": "", "Enable New Sign Ups": "نئے سائن اپس کو فعال کریں", "Enabled": "فعال کردیا گیا ہے", "Endpoint URL": "", "Enforce Temporary Chat": "", "Enhance": "", "Ensure your CSV file includes 4 columns in this order: Name, Email, Password, Role.": "یقینی بنائیں کہ آپ کی CSV فائل میں 4 کالم اس ترتیب میں شامل ہوں: نام، ای میل، پاس ورڈ، کردار", "Enter {{role}} message here": "یہاں {{کردار}} پیغام درج کریں", "Enter a detail about yourself for your LLMs to recall": "اپنی ذات کے بارے میں کوئی تفصیل درج کریں تاکہ آپ کے LLMs اسے یاد رکھ سکیں", "Enter a title for the pending user info overlay. Leave empty for default.": "", "Enter a watermark for the response. Leave empty for none.": "", "Enter api auth string (e.g. username:password)": "اے پی آئی اتھ سٹرنگ درج کریں (مثال کے طور پر: صارف نام:پاس ورڈ)", "Enter Application DN": "", "Enter Application DN Password": "", "Enter Bing Search V7 Endpoint": "", "Enter Bing Search V7 Subscription Key": "", "Enter BM25 Weight": "", "Enter Bocha Search API Key": "", "Enter Brave Search API Key": "بریو سرچ API کلید درج کریں", "Enter certificate path": "", "Enter CFG Scale (e.g. 7.0)": "CFG اسکیل درج کریں (مثلاً 7.0)", "Enter Chunk Overlap": "چنک اوورلیپ درج کریں", "Enter Chunk Size": "چنک سائز درج کریں", "Enter comma-separated \"token:bias_value\" pairs (example: 5432:100, 413:-100)": "", "Enter Config in JSON format": "", "Enter content for the pending user info overlay. Leave empty for default.": "", "Enter Datalab Marker API Key": "", "Enter description": "تفصیل درج کریں", "Enter Docling OCR Engine": "", "Enter Docling OCR Language(s)": "", "Enter Docling Server URL": "", "Enter Document Intelligence Endpoint": "", "Enter Document Intelligence Key": "", "Enter domains separated by commas (e.g., example.com,site.org)": "", "Enter Exa API Key": "", "Enter External Document Loader API Key": "", "Enter External Document Loader URL": "", "Enter External Web Loader API Key": "", "Enter External Web Loader URL": "", "Enter External Web Search API Key": "", "Enter External Web Search URL": "", "Enter Firecrawl API Base URL": "", "Enter Firecrawl API Key": "", "Enter folder name": "", "Enter Github Raw URL": "گیٹ ہب را یو آر ایل درج کریں", "Enter Google PSE API Key": "گوگل PSE API کلید درج کریں", "Enter Google PSE Engine Id": "گوگل پی ایس ای انجن آئی ڈی درج کریں", "Enter Image Size (e.g. 512x512)": "تصویر کا سائز درج کریں (مثال: 512x512)", "Enter Jina API Key": "", "Enter Jupyter Password": "", "Enter Jupyter Token": "", "Enter Jupyter URL": "", "Enter Kagi Search API Key": "", "Enter Key Behavior": "", "Enter language codes": "زبان کے کوڈ درج کریں", "Enter Mistral API Key": "", "Enter Model ID": "ماڈل آئی ڈی درج کریں", "Enter model tag (e.g. {{modelTag}})": "ماڈل ٹیگ داخل کریں (مثال کے طور پر {{modelTag}})", "Enter Mojeek Search API Key": "", "Enter name": "", "Enter New Password": "", "Enter Number of Steps (e.g. 50)": "درج کریں مراحل کی تعداد (جیسے 50)", "Enter Perplexity API Key": "", "Enter Playwright Timeout": "", "Enter Playwright WebSocket URL": "", "Enter proxy URL (e.g. **************************:port)": "", "Enter reasoning effort": "", "Enter Sampler (e.g. Euler a)": "نمونہ درج کریں (مثال: آئلر a)", "Enter Scheduler (e.g. Karras)": "شیڈیولر درج کریں (مثلاً Karras)", "Enter Score": "درجہ درج کریں", "Enter SearchApi API Key": "تلاش API کلید داخل کریں", "Enter SearchApi Engine": "تلاش انجن درج کریں", "Enter Searxng Query URL": "سیرنگ استفسار یو آر ایل درج کریں", "Enter Seed": "", "Enter SerpApi API Key": "", "Enter SerpApi Engine": "", "Enter Serper API Key": "سرپر API کلید داخل کریں", "Enter Serply API Key": "سیرپلی API کلید درج کریں", "Enter Serpstack API Key": "سرپ اسٹیک API کلید درج کریں", "Enter server host": "", "Enter server label": "", "Enter server port": "", "Enter Sougou Search API sID": "", "Enter Sougou Search API SK": "", "Enter stop sequence": "اسٹاپ ترتیب درج کریں", "Enter system prompt": "سسٹم پرامپٹ درج کریں", "Enter system prompt here": "", "Enter Tavily API Key": "<PERSON><PERSON> <PERSON> کلید درج کریں", "Enter Tavily Extract Depth": "", "Enter the public URL of your WebUI. This URL will be used to generate links in the notifications.": "", "Enter the URL of the function to import": "", "Enter the URL to import": "", "Enter Tika Server URL": "ٹیکا سرور یو آر ایل درج کریں", "Enter timeout in seconds": "", "Enter to Send": "", "Enter Top K": "اوپر کے K درج کریں", "Enter Top K Reranker": "", "Enter URL (e.g. http://127.0.0.1:7860/)": "یو آر ایل درج کریں (جیسے کہ http://127.0.0.1:7860/)", "Enter URL (e.g. http://localhost:11434)": "یو آر ایل درج کریں (مثلاً http://localhost:11434)", "Enter Yacy Password": "", "Enter Yacy URL (e.g. http://yacy.example.com:8090)": "", "Enter Yacy Username": "", "Enter your current password": "", "Enter Your Email": "اپنا ای میل درج کریں", "Enter Your Full Name": "اپنا مکمل نام درج کریں", "Enter your message": "اپنا پیغام درج کریں", "Enter your name": "", "Enter Your Name": "", "Enter your new password": "", "Enter Your Password": "اپنا پاس ورڈ درج کریں", "Enter Your Role": "اپنا کردار درج کریں", "Enter Your Username": "", "Enter your webhook URL": "", "Error": "غلطی", "ERROR": "غلطی", "Error accessing Google Drive: {{error}}": "", "Error accessing media devices.": "", "Error starting recording.": "", "Error unloading model: {{error}}": "", "Error uploading file: {{error}}": "", "Evaluations": "تشخیصات", "Everyone": "", "Exa API Key": "", "Example: (&(objectClass=inetOrgPerson)(uid=%s))": "", "Example: ALL": "", "Example: mail": "", "Example: ou=users,dc=foo,dc=example": "", "Example: sAMAccountName or uid or userPrincipalName": "", "Exceeded the number of seats in your license. Please contact support to increase the number of seats.": "", "Exclude": "خارج کریں", "Execute code for analysis": "", "Executing **{{NAME}}**...": "", "Expand": "", "Experimental": "تجرباتی", "Explain": "", "Explore the cosmos": "", "Export": "بر<PERSON>مد کریں", "Export All Archived Chats": "", "Export All Chats (All Users)": "تمام چیٹس برآمد کریں (تمام صارفین)", "Export chat (.json)": "چیٹ برآمد کریں (.json)", "Export Chats": "چیٹس برآمد کریں", "Export Config to JSON File": "کنفیگ کو JSON فائل میں ایکسپورٹ کریں", "Export Functions": "ایکسپورٹ فنکشنز", "Export Models": "ماڈلز برآمد کریں", "Export Presets": "", "Export Prompt Suggestions": "", "Export Prompts": "پرامپٹس برآمد کریں", "Export to CSV": "", "Export Tools": "ایکسپورٹ ٹولز", "External": "", "External Document Loader URL required.": "", "External Task Model": "", "External Web Loader API Key": "", "External Web Loader URL": "", "External Web Search API Key": "", "External Web Search URL": "", "Fade Effect for Streaming Text": "", "Failed to add file.": "فائل شامل کرنے میں ناکام", "Failed to connect to {{URL}} OpenAPI tool server": "", "Failed to copy link": "", "Failed to create API Key.": "API کلید بنانے میں ناکام", "Failed to delete note": "", "Failed to extract content from the file: {{error}}": "", "Failed to extract content from the file.": "", "Failed to fetch models": "", "Failed to generate title": "", "Failed to load chat preview": "", "Failed to load file content.": "", "Failed to read clipboard contents": "کلپ بورڈ مواد کو پڑھنے میں ناکام", "Failed to save connections": "", "Failed to save models configuration": "", "Failed to update settings": "ترتیبات کی تازہ کاری ناکام رہی", "Failed to upload file.": "فائل اپلوڈ کرنے میں ناکامی ہوئی", "Features": "", "Features Permissions": "", "February": "فروری", "Feedback Details": "", "Feedback History": "تاریخ رائے", "Feedbacks": "", "Feel free to add specific details": "تفصیلات شامل کرنے کے لیے آزاد محسوس کریں", "File": "فائل", "File added successfully.": "فائل کامیابی سے شامل ہو گئی", "File content updated successfully.": "فائل مواد کامیابی سے اپ ڈیٹ ہو گیا", "File Mode": "فائل موڈ", "File not found.": "فائل نہیں ملی", "File removed successfully.": "فائل کامیابی سے ہٹا دی گئی", "File size should not exceed {{maxSize}} MB.": "فائل کا سائز {{maxSize}} ایم بی سے زیادہ نہیں ہونا چاہیے", "File Upload": "", "File uploaded successfully": "", "Files": "فائلز", "Filter": "", "Filter is now globally disabled": "فلٹر اب عالمی طور پر غیر فعال ہے", "Filter is now globally enabled": "فلٹر اب عالمی طور پر فعال ہے", "Filters": "فل<PERSON><PERSON><PERSON>", "Fingerprint spoofing detected: Unable to use initials as avatar. Defaulting to default profile image.": "فنگر پرنٹ اسپورفنگ کا پتہ چلا: اوتار کے طور پر ابتدائی حروف استعمال کرنے سے قاصر ڈیفالٹ پروفائل تصویر منتخب کی جا رہی ہے", "Firecrawl API Base URL": "", "Firecrawl API Key": "", "Fluidly stream large external response chunks": "بڑے بیرونی جوابات کے حصوں کو بہاؤ میں منتقل کریں", "Focus chat input": "چیٹ ان پٹ پر توجہ مرکوز کریں", "Folder deleted successfully": "پوشہ کامیابی سے حذف ہو گیا", "Folder Name": "", "Folder name cannot be empty.": "پوشے کا نام خالی نہیں ہو سکتا", "Folder name updated successfully": "فولڈر کا نام کامیابی سے اپ ڈیٹ ہوگیا", "Folder updated successfully": "", "Follow up": "", "Follow Up Generation": "", "Follow Up Generation Prompt": "", "Follow-Up Auto-Generation": "", "Followed instructions perfectly": "ہدایتوں کی مکمل پیروی کی گئی", "Force OCR": "", "Force OCR on all pages of the PDF. This can lead to worse results if you have good text in your PDFs. Defaults to False.": "", "Forge new paths": "", "Form": "فارم", "Format your variables using brackets like this:": "اپنے متغیرات کو اس طرح بریکٹس میں فارمیٹ کریں:", "Forwards system user session credentials to authenticate": "", "Full Context Mode": "", "Function": "فنکشن", "Function Calling": "", "Function created successfully": "فنکشن کامیابی سے تخلیق ہو گیا", "Function deleted successfully": "فنکشن کامیابی کے ساتھ حذف ہو گیا", "Function Description": "", "Function ID": "", "Function imported successfully": "", "Function is now globally disabled": "فنکشن اب عالمی طور پر غیر فعال ہے", "Function is now globally enabled": "فنکشن اب عالمی طور پر فعال ہے", "Function Name": "", "Function updated successfully": "فنکشن کو کامیابی سے اپ ڈیٹ کر دیا گیا", "Functions": "افعال", "Functions allow arbitrary code execution.": "افعال صوابدیدی کوڈ کے اجرا کی اجازت دیتے ہیں", "Functions imported successfully": "فنکشنز کامیابی سے درآمد ہو گئے ہیں", "Gemini": "", "Gemini API Config": "", "Gemini API Key is required.": "", "General": "عمومی", "Generate": "", "Generate an image": "", "Generate Image": "تصویر بنائیں", "Generate prompt pair": "", "Generating search query": "تلاش کے لیے سوالیہ عبارت تیار کی جا رہی ہے", "Generating...": "", "Get information on {{name}} in the UI": "", "Get started": "", "Get started with {{WEBUI_NAME}}": "", "Global": "عالمی", "Good Response": "اچھا جواب", "Google Drive": "", "Google PSE API Key": "گوگل پی ایس ای API کی کلید", "Google PSE Engine Id": "گوگل پی ایس ای انجن آئی ڈی", "Group created successfully": "", "Group deleted successfully": "", "Group Description": "", "Group Name": "", "Group updated successfully": "", "Groups": "", "H1": "", "H2": "", "H3": "", "Haptic Feedback": "ہاپٹک فیڈ بیک", "Hello, {{name}}": "ہیلو، {{name}}", "Help": "مد<PERSON>", "Help us create the best community leaderboard by sharing your feedback history!": "ہمیں بہترین کمیونٹی لیڈر بورڈ بنانے میں مدد کریں، اپنی رائے کی تاریخ شیئر کر کے!", "Hex Color": "", "Hex Color - Leave empty for default color": "", "Hide": "چھپائیں", "Hide from Sidebar": "", "Hide Model": "", "High Contrast Mode": "", "Home": "", "Host": "", "How can I help you today?": "میں آج آپ کی کس طرح مدد کر سکتا ہوں؟", "How would you rate this response?": "", "HTML": "", "Hybrid Search": "مشترکہ تلاش", "I acknowledge that I have read and I understand the implications of my action. I am aware of the risks associated with executing arbitrary code and I have verified the trustworthiness of the source.": "میں اقرار کرتا ہوں کہ میں نے پڑھ لیا ہے اور میں اپنی کارروائی کے مضمرات سمجھتا ہوں میں اس بات سے واقف ہوں کہ بلاوجہ کوڈ چلانے کے ساتھ منسلک خطرات ہوتے ہیں اور میں نے ماخذ کی اعتمادیت کی تصدیق کی ہے", "ID": "شناخت", "iframe Sandbox Allow Forms": "", "iframe Sandbox Allow Same Origin": "", "Ignite curiosity": "", "Image": "", "Image Compression": "", "Image Compression Height": "", "Image Compression Width": "", "Image Generation": "", "Image Generation (Experimental)": "تصویر کی تخلیق (تجرباتی)", "Image Generation Engine": "امیج جنریشن انجن", "Image Max Compression Size": "", "Image Max Compression Size height": "", "Image Max Compression Size width": "", "Image Prompt Generation": "", "Image Prompt Generation Prompt": "", "Image Settings": "تصویری ترتیبات", "Images": "تصاویر", "Import": "", "Import Chats": "چیٹس درآمد کریں", "Import Config from JSON File": "JSON فائل سے تشکیلات درآمد کریں", "Import From Link": "", "Import Functions": "درآمد فنکشنز", "Import Models": "ماڈلز درآمد کریں", "Import Notes": "", "Import Presets": "", "Import Prompt Suggestions": "", "Import Prompts": "پرامپٹس درآمد کریں", "Import Tools": "امپورٹ ٹولز", "Include": "شامل کریں", "Include `--api-auth` flag when running stable-diffusion-webui": "`--api-auth` پرچم کو چلانے کے وقت شامل کریں stable-diffusion-webui", "Include `--api` flag when running stable-diffusion-webui": "اسٹیبل-ڈیفیوژن-ویب یو آئی چلانے کے دوران `--api` فلیگ شامل کریں", "Influences how quickly the algorithm responds to feedback from the generated text. A lower learning rate will result in slower adjustments, while a higher learning rate will make the algorithm more responsive.": "", "Info": "معلومات", "Inject the entire content as context for comprehensive processing, this is recommended for complex queries.": "", "Input commands": "کمانڈز داخل کریں", "Input Variables": "", "Insert": "", "Insert Follow-Up Prompt to Input": "", "Insert Prompt as Rich Text": "", "Install from Github URL": "گِٹ حب یو آر ایل سے انسٹال کریں", "Instant Auto-Send After Voice Transcription": "آواز کی نقل کے بعد فوری خودکار بھیجنا", "Integration": "", "Interface": "انٹرفیس", "Invalid file content": "", "Invalid file format.": "غلط فائل فارمیٹ", "Invalid JSON file": "", "Invalid Tag": "غلط ٹیگ", "is typing...": "", "Italic": "", "January": "جنوری", "Jina API Key": "", "join our Discord for help.": "مدد کے لئے ہمارے ڈسکارڈ میں شامل ہوں", "JSON": "JSON", "JSON Preview": "JSON پیش منظر", "July": "جولا<PERSON>ی", "June": "جون", "Jupyter Auth": "", "Jupyter URL": "", "JWT Expiration": "JWT کی میعاد ختم ہونا", "JWT Token": "JWT ٹوکن", "Kagi Search API Key": "", "Keep Follow-Up Prompts in Chat": "", "Keep in Sidebar": "", "Key": "", "Keyboard shortcuts": "کی بورڈ شارٹ کٹس", "Knowledge": "علم", "Knowledge Access": "", "Knowledge Base": "", "Knowledge created successfully.": "علم کامیابی سے تخلیق کیا گیا", "Knowledge deleted successfully.": "معلومات کامیابی سے حذف ہو گئیں", "Knowledge Public Sharing": "", "Knowledge reset successfully.": "علم کو کامیابی کے ساتھ دوبارہ ترتیب دیا گیا", "Knowledge updated successfully": "علم کامیابی سے تازہ کر دیا گیا ہے", "Kokoro.js (Browser)": "", "Kokoro.js Dtype": "", "Label": "", "Landing Page Mode": "لینڈر صفحہ موڈ", "Language": "زبان", "Language Locales": "", "Languages": "", "Last Active": "آخری سرگرمی", "Last Modified": "آخری ترمیم", "Last reply": "", "LDAP": "", "LDAP server updated": "", "Leaderboard": "لیڈر بورڈ", "Learn more about OpenAPI tool servers.": "", "Leave empty for no compression": "", "Leave empty for unlimited": "لامحدود کے لیے خالی چھوڑیں", "Leave empty to include all models from \"{{url}}\" endpoint": "", "Leave empty to include all models from \"{{url}}/api/tags\" endpoint": "", "Leave empty to include all models from \"{{url}}/models\" endpoint": "", "Leave empty to include all models or select specific models": "تمام ماڈلز کو شامل کرنے کے لئے خالی چھوڑ دیں یا مخصوص ماڈلز منتخب کریں", "Leave empty to use the default prompt, or enter a custom prompt": "خالی چھوڑیں تاکہ ڈیفالٹ پرامپٹ استعمال ہو، یا ایک حسب ضرورت پرامپٹ درج کریں", "Leave model field empty to use the default model.": "", "License": "", "Lift List": "", "Light": "روشنی", "Listening...": "سن رہے ہیں...", "Llama.cpp": "", "LLMs can make mistakes. Verify important information.": "ایل ایل ایم غلطیاں کر سکتے ہیں اہم معلومات کی تصدیق کریں", "Loader": "", "Loading Kokoro.js...": "", "Local": "", "Local Task Model": "", "Location access not allowed": "", "Lost": "گم شدہ", "LTR": "بائیں سے دائیں", "Made by Open WebUI Community": "اوپن ویب یو آئی کمیونٹی کی جانب سے تیار کردہ", "Make password visible in the user interface": "", "Make sure to enclose them with": "انہیں کے ساتھ شامل کریں", "Make sure to export a workflow.json file as API format from ComfyUI.": "یقینی بنائیں کہ ComfyUI سے workflow.json فائل کو API فارمیٹ میں ایکسپورٹ کریں", "Manage": "مینیج کریں", "Manage Direct Connections": "", "Manage Models": "", "Manage Ollama": "", "Manage Ollama API Connections": "", "Manage OpenAI API Connections": "", "Manage Pipelines": "پائپ لائنز کا نظم کریں", "Manage Tool Servers": "", "March": "ما<PERSON><PERSON>", "Markdown": "", "Markdown (Header)": "", "Max Speakers": "", "Max Upload Count": "زیادہ سے زیادہ اپلوڈ تعداد", "Max Upload Size": "زیادہ سے زیادہ اپلوڈ سائز", "Maximum of 3 models can be downloaded simultaneously. Please try again later.": "بیک وقت زیادہ سے زیادہ 3 ماڈل ڈاؤن لوڈ کیے جا سکتے ہیں براہ کرم بعد میں دوبارہ کوشش کریں", "May": "م<PERSON>ی", "Memories accessible by LLMs will be shown here.": "یہاں LLMs کے ذریعہ قابل رسائی یادیں دکھائی جائیں گی", "Memory": "میموری", "Memory added successfully": "میموری کامیابی سے شامل کر دی گئی", "Memory cleared successfully": "یادداشت کامیابی سے صاف ہوگئی", "Memory deleted successfully": "میموری کامیابی سے حذف ہوگئی", "Memory updated successfully": "حافظہ کامیابی سے اپ ڈیٹ کر دیا گیا", "Merge Responses": "جوابات کو یکجا کریں", "Merged Response": "مرکب جواب", "Message rating should be enabled to use this feature": "اس فیچر کو استعمال کرنے کے لئے پیغام کی درجہ بندی فعال کی جانی چاہئے", "Messages you send after creating your link won't be shared. Users with the URL will be able to view the shared chat.": "آپ کے لنک بنانے کے بعد بھیجے گئے پیغامات شیئر نہیں کیے جائیں گے یو آر ایل والے صارفین شیئر کیا گیا چیٹ دیکھ سکیں گے", "Microsoft OneDrive": "", "Microsoft OneDrive (personal)": "", "Microsoft OneDrive (work/school)": "", "Mistral OCR": "", "Mistral OCR API Key required.": "", "Model": "ماڈل", "Model '{{modelName}}' has been successfully downloaded.": "ماڈل '{{modelName}}' کامیابی سے ڈاؤن لوڈ ہو گیا ہے", "Model '{{modelTag}}' is already in queue for downloading.": "ماڈل '{{modelTag}}' پہلے ہی ڈاؤن لوڈ کے لیے قطار میں ہے", "Model {{modelId}} not found": "ماڈل {{modelId}} نہیں ملا", "Model {{modelName}} is not vision capable": "ماڈل {{modelName}} بصری صلاحیت نہیں رکھتا", "Model {{name}} is now {{status}}": "ماڈل {{name}} اب {{status}} ہے", "Model {{name}} is now hidden": "", "Model {{name}} is now visible": "", "Model accepts file inputs": "", "Model accepts image inputs": "ماڈل تصویری ان پٹس قبول کرتا ہے", "Model can execute code and perform calculations": "", "Model can generate images based on text prompts": "", "Model can search the web for information": "", "Model created successfully!": "ماڈل کامیابی سے تیار کر دیا گیا!", "Model filesystem path detected. Model shortname is required for update, cannot continue.": "ماڈل فائل سسٹم کا راستہ مل گیا ماڈل کا مختصر نام اپڈیٹ کے لیے ضروری ہے، جاری نہیں رہ سکتا", "Model Filtering": "", "Model ID": "ماڈل آئی ڈی", "Model IDs": "", "Model Name": "ماڈل نام", "Model not selected": "ماڈل منتخب نہیں ہوا", "Model Params": "ماڈل پیرامیٹرز", "Model Permissions": "", "Model unloaded successfully": "", "Model updated successfully": "ماڈل کامیابی کے ساتھ اپ ڈیٹ ہو گیا", "Model(s) do not support file upload": "", "Modelfile Content": "ماڈل فائل مواد", "Models": "ماڈلز", "Models Access": "", "Models configuration saved successfully": "", "Models Public Sharing": "", "Mojeek Search API Key": "", "more": "مزی<PERSON>", "More": "مزی<PERSON>", "Name": "نام", "Name your knowledge base": "", "Native": "", "New Chat": "نئی بات چیت", "New Folder": "", "New Function": "", "New Note": "", "New Password": "نیا پاس ورڈ", "New Tool": "", "new-channel": "", "Next message": "", "No chats found": "", "No chats found for this user.": "", "No chats found.": "", "No content": "", "No content found": "کوئی مواد نہیں ملا", "No content found in file.": "", "No content to speak": "بولنے کے لیے کوئی مواد نہیں", "No distance available": "فاصلہ دستیاب نہیں ہے", "No feedbacks found": "کوئی تبصرے نہیں ملے", "No file selected": "کوئی فائل منتخب نہیں کی گئی", "No groups with access, add a group to grant access": "", "No HTML, CSS, or JavaScript content found.": "کوئی HTML، CSS، یا جاوا اسکرپٹ مواد نہیں ملا", "No inference engine with management support found": "", "No knowledge found": "کوئی معلومات نہیں ملی", "No memories to clear": "", "No model IDs": "", "No models found": "کوئی ماڈل نہیں ملا", "No models selected": "", "No Notes": "", "No results found": "کوئی نتائج نہیں ملے", "No search query generated": "کوئی تلاش کی درخواست نہیں بنائی گئی", "No source available": "ماخذ دستیاب نہیں ہے", "No users were found.": "", "No valves to update": "تازہ کاری کے لئے کوئی والو نہیں", "None": "کوئی نہیں", "Not factually correct": "حقیقت کے مطابق نہیں ہے", "Not helpful": "مددگار نہیں ہے", "Note deleted successfully": "", "Note: If you set a minimum score, the search will only return documents with a score greater than or equal to the minimum score.": "نوٹ: اگر آپ کم از کم سکور سیٹ کرتے ہیں، تو تلاش صرف ان دستاویزات کو واپس کرے گی جن کا سکور کم از کم سکور کے برابر یا اس سے زیادہ ہوگا", "Notes": "نوٹس", "Notification Sound": "", "Notification Webhook": "", "Notifications": "اطلاعات", "November": "نومبر", "OAuth ID": "<PERSON><PERSON><PERSON> ڈی", "October": "اکتوبر", "Off": "بند", "Okay, Let's Go!": "ٹھیک ہے، چلیں!", "OLED Dark": "او ایل ای ڈی ڈارک", "Ollama": "اولامہ", "Ollama API": "اولامہ API", "Ollama API settings updated": "", "Ollama Version": "اولاما ورژن", "On": "چالو", "OneDrive": "", "Only active when \"Paste Large Text as File\" setting is toggled on.": "", "Only active when the chat input is in focus and an LLM is generating a response.": "", "Only alphanumeric characters and hyphens are allowed": "", "Only alphanumeric characters and hyphens are allowed in the command string.": "کمانڈ سٹرنگ میں صرف حروفی، عددی کردار اور ہائفن کی اجازت ہے", "Only collections can be edited, create a new knowledge base to edit/add documents.": "صرف مجموعے ترمیم کیے جا سکتے ہیں، دستاویزات کو ترمیم یا شامل کرنے کے لیے نیا علمی بنیاد بنائیں", "Only markdown files are allowed": "", "Only select users and groups with permission can access": "", "Oops! Looks like the URL is invalid. Please double-check and try again.": "اوہ! لگتا ہے کہ یو آر ایل غلط ہے براۂ کرم دوبارہ چیک کریں اور دوبارہ کوشش کریں", "Oops! There are files still uploading. Please wait for the upload to complete.": "اوہ! کچھ فائلیں ابھی بھی اپ لوڈ ہو رہی ہیں براہ کرم اپ لوڈ مکمل ہونے کا انتظار کریں", "Oops! There was an error in the previous response.": "اوہ! پچھلے جواب میں ایک غلطی تھی", "Oops! You're using an unsupported method (frontend only). Please serve the WebUI from the backend.": "اوہ! آپ ایک غیر معاون طریقہ استعمال کر رہے ہیں (صرف فرنٹ اینڈ) براہ کرم ویب یو آئی کو بیک اینڈ سے پیش کریں", "Open file": "فائل کھولیں", "Open in full screen": "پوری اسکرین میں کھولیں", "Open modal to configure connection": "", "Open new chat": "نیا چیٹ کھولیں", "Open WebUI can use tools provided by any OpenAPI server.": "", "Open WebUI uses faster-whisper internally.": "اوپن ویب یو آئی اندرونی طور پر فاسٹر وِسپر استعمال کرتا ہے", "Open WebUI uses SpeechT5 and CMU Arctic speaker embeddings.": "", "Open WebUI version (v{{OPEN_WEBUI_VERSION}}) is lower than required version (v{{REQUIRED_VERSION}})": "اوپن WebUI ورژن (v{{OPEN_WEBUI_VERSION}}) مطلوبہ ورژن (v{{REQUIRED_VERSION}}) سے کم ہے", "OpenAI": "اوپن اے آئی", "OpenAI API": "اوپن اے آئی اے پی آئی\n", "OpenAI API Config": "اوپن اے آئی API ترتیب", "OpenAI API Key is required.": "اوپن اے آئی API کلید درکار ہے", "OpenAI API settings updated": "", "OpenAI URL/Key required.": "اوپن اے آئی یو آر ایل/کلید درکار ہے", "openapi.json URL or Path": "", "Options for running a local vision-language model in the picture description. The parameters refer to a model hosted on Hugging Face. This parameter is mutually exclusive with picture_description_api.": "", "or": "یا", "Ordered List": "", "Organize your users": "", "Other": "دیگر", "OUTPUT": "آؤٹ پٹ", "Output format": "آؤٹ پٹ فارمیٹ", "Output Format": "", "Overview": "جا<PERSON>زہ", "page": "ص<PERSON><PERSON><PERSON>", "Paginate": "", "Parameters": "", "Password": "پاس ورڈ", "Paste Large Text as File": "", "PDF document (.pdf)": "پی ڈی ایف دستاویز (.pdf)", "PDF Extract Images (OCR)": "پی ڈی ایف سے تصاویر نکالیں (او سی آر)", "pending": "زیر التواء", "Pending": "", "Pending User Overlay Content": "", "Pending User Overlay Title": "", "Permission denied when accessing media devices": "میڈیا آلات تک رسائی کے وقت اجازت مسترد کر دی گئی", "Permission denied when accessing microphone": "مائیکروفون تک رسائی کی اجازت نہیں دی گئی", "Permission denied when accessing microphone: {{error}}": "مائیکروفون تک رسائی کے دوران اجازت مسترد: {{error}}", "Permissions": "", "Perplexity API Key": "", "Perplexity Model": "", "Perplexity Search Context Usage": "", "Personalization": "شخصی ترتیبات", "Picture Description API Config": "", "Picture Description Local Config": "", "Picture Description Mode": "", "Pin": "پن", "Pinned": "پن کیا گیا", "Pioneer insights": "", "Pipe": "", "Pipeline deleted successfully": "پائپ لائن کامیابی سے حذف کر دی گئی", "Pipeline downloaded successfully": "پائپ لائن کامیابی سے ڈاؤن لوڈ ہو گئی", "Pipelines": "پائپ لائنز", "Pipelines Not Detected": "پائپ لائنز کی نشاندہی نہیں ہوئی", "Pipelines Valves": "پائپ لائنز والوز", "Plain text (.md)": "", "Plain text (.txt)": "سادہ متن (.txt)", "Playground": "کھیل کا میدان", "Playwright Timeout (ms)": "", "Playwright WebSocket URL": "", "Please carefully review the following warnings:": "براہ کرم درج ذیل انتباہات کو احتیاط سے پڑھیں:", "Please do not close the settings page while loading the model.": "", "Please enter a prompt": "براہ کرم ایک پرامپٹ درج کریں", "Please enter a valid path": "", "Please enter a valid URL": "", "Please fill in all fields.": "براہ کرم تمام فیلڈز مکمل کریں", "Please select a model first.": "", "Please select a model.": "", "Please select a reason": "براہ کرم ایک وجہ منتخب کریں", "Port": "", "Positive attitude": "مثبت رویہ", "Prefix ID": "", "Prefix ID is used to avoid conflicts with other connections by adding a prefix to the model IDs - leave empty to disable": "", "Prevent file creation": "", "Preview": "", "Previous 30 days": "پچھلے 30 دن", "Previous 7 days": "پچھلے 7 دن", "Previous message": "", "Private": "", "Profile Image": "پروفائل تصویر", "Prompt": "", "Prompt (e.g. Tell me a fun fact about the Roman Empire)": "سوال کریں (مثلاً: مجھے رومن سلطنت کے بارے میں کوئی دلچسپ حقیقت بتائیں)", "Prompt Autocompletion": "", "Prompt Content": "مواد کا آغاز کریں", "Prompt created successfully": "", "Prompt suggestions": "تجاویز کی تجویزات", "Prompt updated successfully": "", "Prompts": "پرومپٹس", "Prompts Access": "", "Prompts Public Sharing": "", "Public": "", "Pull \"{{searchValue}}\" from Ollama.com": "Ollama.com سے \"{{searchValue}}\" کو کھینچیں", "Pull a model from Ollama.com": "Ollama.com سے ماڈل حاصل کریں", "Query Generation Prompt": "", "RAG Template": "آر اے جی سانچہ", "Rating": "در<PERSON><PERSON> بندی", "Re-rank models by topic similarity": "موضوع کی مماثلت کے لحاظ سے ماڈلز کی دوبارہ ترتیب دیں", "Read": "", "Read Aloud": "بُلند آواز میں پڑھیں", "Reason": "", "Reasoning Effort": "", "Record": "", "Record voice": "صوت ریکارڈ کریں", "Redirecting you to Open WebUI Community": "آپ کو اوپن ویب یو آئی کمیونٹی کی طرف ری ڈائریکٹ کیا جا رہا ہے", "Reduces the probability of generating nonsense. A higher value (e.g. 100) will give more diverse answers, while a lower value (e.g. 10) will be more conservative.": "", "Refer to yourself as \"User\" (e.g., \"User is learning Spanish\")": "<PERSON>ود کو \"صارف\" کے طور پر حوالہ دیں (جیسے، \"صارف ہسپانوی سیکھ رہا ہے\")", "References from": "سے حوالہ جات", "Refused when it shouldn't have": "جب انکار نہیں ہونا چاہیے تھا، انکار کر دیا", "Regenerate": "دوبارہ تخلیق کریں", "Reindex": "", "Reindex Knowledge Base Vectors": "", "Release Notes": "ریلیز نوٹس", "Releases": "", "Relevance": "موزونیت", "Relevance Threshold": "", "Remember Dismissal": "", "Remove": "ہٹا دیں", "Remove {{MODELID}} from list.": "", "Remove file": "", "Remove File": "", "Remove image": "", "Remove Model": "ماڈل ہٹائیں", "Remove this tag from list": "", "Rename": "تبدیل نام کریں", "Reorder Models": "", "Reply in Thread": "", "Reranking Engine": "", "Reranking Model": "دوبارہ درجہ بندی کا ماڈل", "Reset": "ری سیٹ", "Reset All Models": "", "Reset Upload Directory": "اپلوڈ ڈائریکٹری کو ری سیٹ کریں", "Reset Vector Storage/Knowledge": "ویكٹر اسٹوریج/علم کو ری سیٹ کریں", "Reset view": "", "Response": "", "Response notifications cannot be activated as the website permissions have been denied. Please visit your browser settings to grant the necessary access.": "جواب کی اطلاعات کو فعال نہیں کیا جا سکتا کیونکہ ویب سائٹ کی اجازتیں مسترد کر دی گئی ہیں براہ کرم اپنے براؤزر کی سیٹنگز پر جائیں تاکہ ضروری رسائی کی اجازت دے سکیں", "Response splitting": "جواب کو تقسیم کرنا", "Response Watermark": "", "Result": "نتیجہ", "Retrieval": "", "Retrieval Query Generation": "", "Rich Text Input for Chat": "چیٹ کے لیے رچ ٹیکسٹ ان پٹ", "RK": "آر کے", "Role": "کردار", "Rosé Pine": "روزے پائن", "Rosé Pine Dawn": "روزے پائن ڈان", "RTL": "آر ٹی ایل", "Run": "چلائیں", "Running": "چل رہا ہے", "Save": "محفوظ کریں", "Save & Create": "محفوظ کریں اور تخلیق کریں", "Save & Update": "محفوظ کریں اور اپ ڈیٹ کریں", "Save As Copy": "کاپی کے طور پر محفوظ کریں", "Save Tag": "ٹیگ محفوظ کریں", "Saved": "محف<PERSON><PERSON> شدہ", "Saving chat logs directly to your browser's storage is no longer supported. Please take a moment to download and delete your chat logs by clicking the button below. Don't worry, you can easily re-import your chat logs to the backend through": "براہ کرم اپنے براؤزر کے اسٹوریج میں چیٹ لاگز کو محفوظ کرنا اب تعاون یافتہ نہیں ہے براہ کرم نیچے دیئے گئے بٹن پر کلک کرکے اپنے چیٹ لاگز کو ڈاؤن لوڈ اور حذف کریں فکر نہ کریں، آپ اپنے چیٹ لاگز کو بیک اینڈ میں دوبارہ آسانی سے درآمد کر سکتے ہیں", "Scroll On Branch Change": "", "Search": "تلاش کریں", "Search a model": "ماڈل تلاش کریں", "Search Base": "", "Search Chats": "چیٹس تلاش کریں", "Search Collection": "مجموعہ تلاش کریں", "Search Filters": "", "search for tags": "ٹیگز کے لیے تلاش کریں", "Search Functions": "تلاش کے افعال", "Search Knowledge": "علم تلاش کریں", "Search Models": "ماڈلز تلاش کریں", "Search Notes": "", "Search options": "", "Search Prompts": "تلاش کے اشارے", "Search Result Count": "تلاش کا نتیجہ شمار ", "Search the internet": "", "Search Tools": "تلاش کے اوزار", "SearchApi API Key": "سرچ اے پی آئی کی API کلید", "SearchApi Engine": "تلاش انجن API", "Searched {{count}} sites": "", "Searching \"{{searchQuery}}\"": "\"{{searchQuery}}\" تلاش کر رہے ہیں", "Searching Knowledge for \"{{searchQuery}}\"": "\"{{searchQuery}}\" کے لیے علم کی تلاش", "Searching the web...": "", "Searxng Query URL": "تلاش کا سوال URL", "See readme.md for instructions": "ہدایات کے لیے readme.md دیکھیں", "See what's new": "نیا کیا ہے دیکھیں", "Seed": "بیج", "Select a base model": "ایک بنیادی ماڈل منتخب کریں", "Select a conversation to preview": "", "Select a engine": "ایک انجن منتخب کریں", "Select a function": "ایک فنکشن منتخب کریں", "Select a group": "", "Select a model": "ایک ماڈل منتخب کریں", "Select a pipeline": "ایک پائپ لائن منتخب کریں", "Select a pipeline url": "پائپ لائن یو آر ایل منتخب کریں", "Select a tool": "ایک ٹول منتخب کریں", "Select an auth method": "", "Select an Ollama instance": "", "Select Engine": "انجن منتخب کریں", "Select Knowledge": "علم منتخب کریں", "Select only one model to call": "صرف ایک ماڈل کو کال کرنے کے لئے منتخب کریں", "Selected model(s) do not support image inputs": "منتخب کردہ ماڈل(ز) تصویری ان پٹ کی حمایت نہیں کرتے", "Semantic distance to query": "سوال کے لیے معنوی فاصلہ", "Send": "بھیجیں", "Send a Message": "پیغام بھیجیں", "Send message": "پیغام بھیجیں", "Sends `stream_options: { include_usage: true }` in the request.\nSupported providers will return token usage information in the response when set.": "درخواست میں `stream_options: { include_usage: true }` بھیجتا ہے\nمعاون فراہم کنندگان، جب سیٹ کیا جاتا ہے تو، جواب میں ٹوکن کے استعمال کی معلومات واپس کر دیں گے", "September": "ستمبر", "SerpApi API Key": "", "SerpApi Engine": "", "Serper API Key": "سرپر API کلید", "Serply API Key": "سرپلی API کی کلید", "Serpstack API Key": "سرپ اسٹیک اے پی آئی کلید", "Server connection verified": "سرور کنکشن تصدیق شدہ ہے", "Set as default": "بطور ڈیفالٹ سیٹ کریں", "Set CFG Scale": "سی ایف جی اسکیل مقرر کریں", "Set Default Model": "ڈیفالٹ ماڈل سیٹ کریں", "Set embedding model": "", "Set embedding model (e.g. {{model}})": "نقش ماڈل مرتب کریں (مثال کے طور پر {{model}})", "Set Image Size": "تصویر کا سائز مقرر کریں", "Set reranking model (e.g. {{model}})": "ری رینکنگ ماڈل سیٹ کریں (مثال کے طور پر {{model}})", "Set Sampler": "نمونہ سیٹ کریں", "Set Scheduler": "شیڈیولر مقرر کریں", "Set Steps": "قدم طے کریں", "Set the number of layers, which will be off-loaded to GPU. Increasing this value can significantly improve performance for models that are optimized for GPU acceleration but may also consume more power and GPU resources.": "", "Set the number of worker threads used for computation. This option controls how many threads are used to process incoming requests concurrently. Increasing this value can improve performance under high concurrency workloads but may also consume more CPU resources.": "", "Set Voice": "آواز کے لئے سیٹ کریں", "Set whisper model": "وِسپر ماڈل مرتب کریں", "Sets a flat bias against tokens that have appeared at least once. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets a scaling bias against tokens to penalize repetitions, based on how many times they have appeared. A higher value (e.g., 1.5) will penalize repetitions more strongly, while a lower value (e.g., 0.9) will be more lenient. At 0, it is disabled.": "", "Sets how far back for the model to look back to prevent repetition.": "", "Sets the random number seed to use for generation. Setting this to a specific number will make the model generate the same text for the same prompt.": "", "Sets the size of the context window used to generate the next token.": "", "Sets the stop sequences to use. When this pattern is encountered, the LLM will stop generating text and return. Multiple stop patterns may be set by specifying multiple separate stop parameters in a modelfile.": "", "Settings": "ترتیبات", "Settings saved successfully!": "ترتیبات کامیابی کے ساتھ محفوظ ہو گئیں!", "Share": "اشتراک کریں", "Share Chat": "چیٹ شیئر کریں", "Share to Open WebUI Community": "اوپن ویب یوآئی کمیونٹی کے ساتھ شیئر کریں\n", "Sharing Permissions": "", "Shortcuts with an asterisk (*) are situational and only active under specific conditions.": "", "Show": "دکھائیں", "Show \"What's New\" modal on login": "", "Show Admin Details in Account Pending Overlay": "اکاؤنٹ پینڈنگ اوورلے میں ایڈمن کی تفصیلات دکھائیں", "Show All": "", "Show image preview": "", "Show Less": "", "Show Model": "", "Show shortcuts": "شارٹ کٹ دکھائیں", "Show your support!": "اپنی حمایت دکھائیں!", "Showcased creativity": "نمائش شدہ تخلیقی صلاحیتیں", "Sign in": "سائن ان کریں", "Sign in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} میں سائن ان کریں", "Sign in to {{WEBUI_NAME}} with LDAP": "", "Sign Out": "سائن آؤٹ کریں", "Sign up": "سائن اپ کریں", "Sign up to {{WEBUI_NAME}}": "{{WEBUI_NAME}} میں سائن اپ کریں", "Significantly improves accuracy by using an LLM to enhance tables, forms, inline math, and layout detection. Will increase latency. Defaults to True.": "", "Signing in to {{WEBUI_NAME}}": "{{WEBUI_NAME}} میں سائن اِن کر رہے ہیں", "Sink List": "", "sk-1234": "", "Skip Cache": "", "Skip the cache and re-run the inference. Defaults to False.": "", "Sougou Search API sID": "", "Sougou Search API SK": "", "Source": "ما<PERSON>ذ", "Speech Playback Speed": "تقریر پلے بیک کی رفتار", "Speech recognition error: {{error}}": "تقریر کی پہچان کی خرابی: {{error}}", "Speech-to-Text": "", "Speech-to-Text Engine": "تقریر-سے-متن انجن", "Stop": "روکیں", "Stop Generating": "", "Stop Sequence": "ترتیب روکیں", "Stream Chat Response": "اسٹریم چیٹ جواب", "Strikethrough": "", "Strip Existing OCR": "", "Strip existing OCR text from the PDF and re-run OCR. Ignored if Force OCR is enabled. Defaults to False.": "", "STT Model": "ایس ٹی ٹی ماڈل", "STT Settings": "ایس ٹی ٹی ترتیبات", "Stylized PDF Export": "", "Subtitle (e.g. about the Roman Empire)": "ذیلی عنوان (جیسے رومن سلطنت کے بارے میں)", "Success": "کامیابی", "Successfully updated.": "کامیابی سے تازہ کاری ہو گئی", "Suggested": "تجویز کردہ", "Support": "مدد کریں", "Support this plugin:": "اس پلگ ان کی حمایت کریں:", "Supported MIME Types": "", "Sync directory": "ڈائریکٹری مطابقت پذیری کریں", "System": "سسٹم", "System Instructions": "نظام کی ہدایات", "System Prompt": "سسٹم پرومپٹ", "Tags": "", "Tags Generation": "", "Tags Generation Prompt": "پرمپٹ کے لیے ٹیگز بنائیں", "Tail free sampling is used to reduce the impact of less probable tokens from the output. A higher value (e.g., 2.0) will reduce the impact more, while a value of 1.0 disables this setting.": "", "Talk to model": "", "Tap to interrupt": "رکنے کے لئے ٹچ کریں", "Task List": "", "Task Model": "", "Tasks": "", "Tavily API Key": "ٹاویلی API کلید", "Tavily Extract Depth": "", "Tell us more:": "ہمیں مزید بتائیں:", "Temperature": "در<PERSON>ہ حرارت", "Temporary Chat": "عارضی چیٹ", "Text Splitter": "متن تقسیم کنندہ", "Text-to-Speech": "", "Text-to-Speech Engine": "ٹیکسٹ ٹو اسپیچ انجن", "Thanks for your feedback!": "آپ کی رائے کا شکریہ!", "The Application Account DN you bind with for search": "", "The base to search for users": "", "The batch size determines how many text requests are processed together at once. A higher batch size can increase the performance and speed of the model, but it also requires more memory.": "", "The developers behind this plugin are passionate volunteers from the community. If you find this plugin helpful, please consider contributing to its development.": "اس پلگ ان کے پیچھے موجود ڈویلپرز کمیونٹی کے پرجوش رضاکار ہیں اگر آپ کو یہ پلگ ان مددگار لگتا ہے تو برائے مہربانی اس کی ترقی میں اپنا حصہ ڈالنے پر غور کریں", "The evaluation leaderboard is based on the Elo rating system and is updated in real-time.": "تشخیصی لیڈربورڈ ایلو ریٹنگ سسٹم پر مبنی ہے اور یہ حقیقی وقت میں اپ ڈیٹ ہوتا ہے", "The format to return a response in. Format can be json or a JSON schema.": "", "The height in pixels to compress images to. Leave empty for no compression.": "", "The language of the input audio. Supplying the input language in ISO-639-1 (e.g. en) format will improve accuracy and latency. Leave blank to automatically detect the language.": "", "The LDAP attribute that maps to the mail that users use to sign in.": "", "The LDAP attribute that maps to the username that users use to sign in.": "", "The leaderboard is currently in beta, and we may adjust the rating calculations as we refine the algorithm.": "لیڈر بورڈ اس وقت بیٹا مرحلے میں ہے، اور جیسے جیسے ہم الگورتھم کو بہتر بنائیں گے ہم ریٹنگ کیلکولیشن کو ایڈجسٹ کرسکتے ہیں", "The maximum file size in MB. If the file size exceeds this limit, the file will not be uploaded.": "زیادہ سے زیادہ فائل سائز ایم بی میں اگر فائل سائز اس حد سے تجاوز کر جاتا ہے، تو فائل اپ لوڈ نہیں ہوگی", "The maximum number of files that can be used at once in chat. If the number of files exceeds this limit, the files will not be uploaded.": "چیٹ میں ایک وقت میں استعمال ہونے والی فائلوں کی زیادہ سے زیادہ تعداد اگر فائلوں کی تعداد اس حد سے تجاوز کر جائے تو فائلیں اپلوڈ نہیں کی جائیں گی", "The output format for the text. Can be 'json', 'markdown', or 'html'. Defaults to 'markdown'.": "", "The score should be a value between 0.0 (0%) and 1.0 (100%).": "سکور کی قیمت کو 0.0 (0%) اور 1.0 (100%) کے درمیان ہونا چاہیے", "The temperature of the model. Increasing the temperature will make the model answer more creatively.": "", "The width in pixels to compress images to. Leave empty for no compression.": "", "Theme": "تھیم", "Thinking...": "سوچ رہا ہے...", "This action cannot be undone. Do you wish to continue?": "یہ عمل واپس نہیں کیا جا سکتا کیا آپ جاری رکھنا چاہتے ہیں؟", "This channel was created on {{createdAt}}. This is the very beginning of the {{channelName}} channel.": "", "This chat won't appear in history and your messages will not be saved.": "", "This ensures that your valuable conversations are securely saved to your backend database. Thank you!": "یہ یقینی بناتا ہے کہ آپ کی قیمتی گفتگو محفوظ طریقے سے آپ کے بیک اینڈ ڈیٹا بیس میں محفوظ کی گئی ہیں شکریہ!", "This feature is experimental and may be modified or discontinued without notice.": "", "This is an experimental feature, it may not function as expected and is subject to change at any time.": "یہ ایک تجرباتی خصوصیت ہے، یہ متوقع طور پر کام نہ کر سکتی ہو اور کسی بھی وقت تبدیل کی جا سکتی ہے", "This model is not publicly available. Please select another model.": "", "This option controls how long the model will stay loaded into memory following the request (default: 5m)": "", "This option controls how many tokens are preserved when refreshing the context. For example, if set to 2, the last 2 tokens of the conversation context will be retained. Preserving context can help maintain the continuity of a conversation, but it may reduce the ability to respond to new topics.": "", "This option enables or disables the use of the reasoning feature in Ollama, which allows the model to think before generating a response. When enabled, the model can take a moment to process the conversation context and generate a more thoughtful response.": "", "This option sets the maximum number of tokens the model can generate in its response. Increasing this limit allows the model to provide longer answers, but it may also increase the likelihood of unhelpful or irrelevant content being generated.": "", "This option will delete all existing files in the collection and replace them with newly uploaded files.": "اس اختیار سے مجموعہ میں موجود تمام فائلز حذف ہو جائیں گی اور ان کی جگہ نئی اپ لوڈ کردہ فائلز لی جائیں گی", "This response was generated by \"{{model}}\"": "یہ جواب \"{{model}}\" کے ذریعہ تیار کیا گیا", "This will delete": "یہ حذف کر دے گا", "This will delete <strong>{{NAME}}</strong> and <strong>all its contents</strong>.": "یہ <strong>{{NAME}}</strong> اور <strong>اس کے تمام مواد</strong> کو حذف کر دے گا", "This will delete all models including custom models": "", "This will delete all models including custom models and cannot be undone.": "", "This will reset the knowledge base and sync all files. Do you wish to continue?": "یہ علمی بنیاد کو دوبارہ ترتیب دے گا اور تمام فائلز کو متوازن کرے گا کیا آپ جاری رکھنا چاہتے ہیں؟", "Thorough explanation": "مکمل وضاحت", "Thought for {{DURATION}}": "", "Thought for {{DURATION}} seconds": "", "Tika": "ٹیکہ", "Tika Server URL required.": "ٹکا سرور یو آر ایل درکار ہے", "Tiktoken": "ٹک ٹوکن", "Tip: Update multiple variable slots consecutively by pressing the tab key in the chat input after each replacement.": "ترکیب: ہر تبدیلی کے بعد چیٹ ان پٹ میں ٹیب کی کلید دباکر متعدد متغیر سلاٹوں کو مسلسل اپ ڈیٹ کریں", "Title": "عنوان", "Title (e.g. Tell me a fun fact)": "عنوان (مثال کے طور پر، مجھے ایک دلچسپ حقیقت بتائیں)", "Title Auto-Generation": "خود<PERSON>ار عنوان تخلیق", "Title cannot be an empty string.": "عنوان خالی اسٹرنگ نہیں ہو سکتا", "Title Generation": "", "Title Generation Prompt": "سرخی بنانے کی ہدایت", "TLS": "", "To access the available model names for downloading,": "ڈاؤن لوڈ کرنے کے لئے دستیاب ماڈل کے ناموں تک رسائی حاصل کرنے کے لئے،", "To access the GGUF models available for downloading,": "GGUF ماڈلز تک رسائی حاصل کرنے کے لئے جو ڈاؤنلوڈنگ کے لئے دستیاب ہیں،", "To access the WebUI, please reach out to the administrator. Admins can manage user statuses from the Admin Panel.": "ویب یو آئی تک رسائی حاصل کرنے کے لیے براہ کرم منتظم سے رابطہ کریں ایڈمنز صارف کی حالت کو ایڈمن پینل سے منظم کر سکتے ہیں", "To attach knowledge base here, add them to the \"Knowledge\" workspace first.": "یہاں معلوماتی بنیاد منسلک کرنے کے لیے، پہلے انہیں \"معلومات\" ورک اسپیس میں شامل کریں", "To learn more about available endpoints, visit our documentation.": "", "To learn more about powerful prompt variables, click here": "", "To protect your privacy, only ratings, model IDs, tags, and metadata are shared from your feedback—your chat logs remain private and are not included.": "آپ کی رازداری کے تحفظ کے لئے، صرف درجہ بندی، ماڈل IDs، ٹیگز، اور میٹا ڈیٹا آپ کے فیڈ بیک سے شیئر کیے جاتے ہیں - آپ کی چیٹ کی تفصیلات نجی رہتی ہیں اور شامل نہیں کی جاتیں ", "To select actions here, add them to the \"Functions\" workspace first.": "عمل کا انتخاب کرنے کے لیے، پہلے انہیں \"افعال\" ورک اسپیس میں شامل کریں", "To select filters here, add them to the \"Functions\" workspace first.": "یہاں فلٹرز منتخب کرنے کے لئے، پہلے انہیں \"فیچرز\" ورک اسپیس میں شامل کریں", "To select toolkits here, add them to the \"Tools\" workspace first.": "یہاں ٹول کٹس منتخب کرنے کے لیے، پہلے انہیں \"ٹولز\" ورک اسپیس میں شامل کریں", "Toast notifications for new updates": "نئے اپڈیٹس کے لئے ٹوسٹ نوٹیفیکیشنز", "Today": "آج", "Toggle search": "", "Toggle settings": "ترتیبات کو تبدیل کریں", "Toggle sidebar": "سائڈبار کو ٹوگل کریں", "Toggle whether current connection is active.": "", "Token": "ٹوکَن", "Too verbose": "بہت زیادہ طویل", "Tool created successfully": "اوزار کامیابی کے ساتھ تخلیق کیا گیا", "Tool deleted successfully": "آلہ کامیابی سے حذف ہو گیا", "Tool Description": "", "Tool ID": "", "Tool imported successfully": "اوزار کامیابی کے ساتھ درآمد ہوا", "Tool Name": "", "Tool Servers": "", "Tool updated successfully": "ٹول کامیابی سے اپ ڈیٹ ہو گیا ہے", "Tools": "اوزار", "Tools Access": "", "Tools are a function calling system with arbitrary code execution": "ٹولز ایک فنکشن کالنگ سسٹم ہیں جس میں مرضی کے مطابق کوڈ چلایا جاتا ہے", "Tools Function Calling Prompt": "", "Tools have a function calling system that allows arbitrary code execution.": "ٹولز کے پاس ایک فنکشن کالنگ سسٹم ہے جو اختیاری کوڈ کی عمل درآمد کی اجازت دیتا ہے", "Tools Public Sharing": "", "Top K": "اوپر کے K", "Top K Reranker": "", "Transformers": "", "Trouble accessing Ollama?": "Ollama تک رسائی میں مشکل؟", "Trust Proxy Environment": "", "TTS Model": "ٹی ٹی ایس ماڈل", "TTS Settings": "ٹی ٹی ایس ترتیبات", "TTS Voice": "ٹی ٹی ایس آواز", "Type": "ٹائپ کریں", "Type Hugging Face Resolve (Download) URL": "قسم ہگنگ فیس ری زولو (ڈاؤن لوڈ) یو آر ایل", "Uh-oh! There was an issue with the response.": "", "UI": "صارف انٹرفیس", "Unarchive All": "", "Unarchive All Archived Chats": "", "Unarchive Chat": "", "Underline": "", "Unloads {{FROM_NOW}}": "", "Unlock mysteries": "", "Unpin": "ان پن کریں", "Unravel secrets": "", "Unsupported file type.": "", "Untagged": "<PERSON><PERSON>ر مہر شدہ", "Untitled": "", "Update": "اپ ڈیٹ کریں", "Update and Copy Link": "اپڈیٹ اور لنک کاپی کریں", "Update for the latest features and improvements.": "تازہ ترین خصوصیات اور بہتریوں کے لیے اپ ڈیٹ کریں", "Update password": "پاس ورڈ اپ ڈیٹ کریں", "Updated": "اپ ڈیٹ کیا گیا", "Updated at": "پر تازہ کاری کی گئی ", "Updated At": "تازہ کاری کی گئی تاریخ پر", "Upgrade to a licensed plan for enhanced capabilities, including custom theming and branding, and dedicated support.": "", "Upload": "اپ لوڈ کریں", "Upload a GGUF model": "جی جی یو ایف ماڈل اپلوڈ کریں", "Upload Audio": "", "Upload directory": "ڈائریکٹری اپلوڈ کریں", "Upload files": "فائلیں اپلوڈ کریں", "Upload Files": "فائلیں اپ لوڈ کریں", "Upload Pipeline": "اپ لوڈ پائپ لائن", "Upload Progress": "اپ لوڈ کی پیش رفت", "URL": "", "URL Mode": "یو آر ایل موڈ", "Usage": "", "Use '#' in the prompt input to load and include your knowledge.": "پرامپٹ ان پٹ میں '#' استعمال کریں تاکہ اپنی معلومات کو لوڈ اور شامل کریں", "Use Gravatar": "گراویٹر استعمال کریں", "Use groups to group your users and assign permissions.": "", "Use Initials": "ابتدائیات استعمال کریں", "Use LLM": "", "Use no proxy to fetch page contents.": "", "Use proxy designated by http_proxy and https_proxy environment variables to fetch page contents.": "", "user": "صارف", "User": "صارف", "User location successfully retrieved.": "صارف کا مقام کامیابی سے حاصل کیا گیا", "User menu": "", "User Webhooks": "", "Username": "", "Users": "صارفین", "Using Entire Document": "", "Using Focused Retrieval": "", "Using the default arena model with all models. Click the plus button to add custom models.": "تمام ماڈلز کے ساتھ ڈیفالٹ ارینا ماڈل استعمال کریں حسب ضرورت ماڈلز شامل کرنے کے لیے پلس بٹن پر کلک کریں", "Valid time units:": "درست وقت کی اکائیاں:", "Valves": "والو", "Valves updated": "والوز کو اپ ڈیٹ کر دیا گیا", "Valves updated successfully": "والو کامیابی کے ساتھ اپ ڈیٹ ہو گئے", "variable": "متغ<PERSON>ر", "Verify Connection": "", "Verify SSL Certificate": "", "Version": "ورژن", "Version {{selectedVersion}} of {{totalVersions}}": "ورژن {{selectedVersion}} کا {{totalVersions}} میں سے", "View Replies": "", "View Result from **{{NAME}}**": "", "Visibility": "", "Vision": "", "Voice": "<PERSON><PERSON><PERSON><PERSON>", "Voice Input": "آواز داخل کریں", "Voice mode": "", "Warning": "انتباہ", "Warning:": "انتباہ:", "Warning: Enabling this will allow users to upload arbitrary code on the server.": "", "Warning: If you update or change your embedding model, you will need to re-import all documents.": "انتباہ: اگر آپ اپنا ایمبیڈنگ ماڈل اپ ڈیٹ یا تبدیل کرتے ہیں تو آپ کو تمام دستاویزات کو دوبارہ درآمد کرنے کی ضرورت ہوگی", "Warning: Jupyter execution enables arbitrary code execution, posing severe security risks—proceed with extreme caution.": "", "Web": "ویب", "Web API": "ویب اے پی آئی", "Web Loader Engine": "", "Web Search": "ویب تلاش کریں", "Web Search Engine": "ویب تلاش انجن", "Web Search in Chat": "", "Web Search Query Generation": "", "Webhook URL": "ویب ہُک یو آر ایل", "WebUI Settings": "ویب UI ترتیبات", "WebUI URL": "", "WebUI will make requests to \"{{url}}\"": "", "WebUI will make requests to \"{{url}}/api/chat\"": "", "WebUI will make requests to \"{{url}}/chat/completions\"": "", "Weight of BM25 Retrieval": "", "What are you trying to achieve?": "", "What are you working on?": "", "What's New in": "میں نیا کیا ہے", "When enabled, the model will respond to each chat message in real-time, generating a response as soon as the user sends a message. This mode is useful for live chat applications, but may impact performance on slower hardware.": "", "wherever you are": "", "Whether to paginate the output. Each page will be separated by a horizontal rule and page number. Defaults to False.": "", "Whisper (Local)": "سرگوشی (مقا<PERSON>ی)", "Why?": "", "Widescreen Mode": "وائڈ اسکرین موڈ", "Won": "جیتا", "Works together with top-k. A higher value (e.g., 0.95) will lead to more diverse text, while a lower value (e.g., 0.5) will generate more focused and conservative text.": "", "Workspace": "ورک اسپیس", "Workspace Permissions": "", "Write": "", "Write a prompt suggestion (e.g. Who are you?)": "(مثال کے طور پر: آپ کون ہیں؟) ایک تجویز لکھیے", "Write a summary in 50 words that summarizes [topic or keyword].": "موضوع یا کلیدی لفظ کا خلاصہ 50 الفاظ میں لکھیں", "Write something...": "کچھ لکھیں...", "Yacy Instance URL": "", "Yacy Password": "", "Yacy Username": "", "Yesterday": "کل", "You": "آپ", "You are currently using a trial license. Please contact support to upgrade your license.": "", "You can only chat with a maximum of {{maxCount}} file(s) at a time.": "آپ ایک وقت میں زیادہ سے زیادہ {{maxCount}} فائل(وں) کے ساتھ صرف چیٹ کر سکتے ہیں", "You can personalize your interactions with LLMs by adding memories through the 'Manage' button below, making them more helpful and tailored to you.": "آپ نیچے موجود 'Manage' بٹن کے ذریعے LLMs کے ساتھ اپنی بات چیت کو یادداشتیں شامل کرکے ذاتی بنا سکتے ہیں، جو انہیں آپ کے لیے زیادہ مددگار اور آپ کے متعلق بنائے گی", "You cannot upload an empty file.": "آپ خالی فائل اپلوڈ نہیں کر سکتے", "You do not have permission to upload files.": "", "You have no archived conversations.": "آپ کے پاس کوئی محفوظ شدہ مکالمات نہیں ہیں", "You have shared this chat": "آپ نے یہ چیٹ شیئر کی ہے", "You're a helpful assistant.": "آپ ایک معاون معاون ہیں", "You're now logged in.": "آپ اب لاگ ان ہو چکے ہیں", "Your account status is currently pending activation.": "آپ کے اکاؤنٹ کی حالت فی الحال فعال ہونے کے منتظر ہے", "Your entire contribution will go directly to the plugin developer; Open WebUI does not take any percentage. However, the chosen funding platform might have its own fees.": "آپ کی پوری شراکت براہ راست پلگ ان ڈیولپر کو جائے گی؛ اوپن ویب یو آئی کوئی فیصد نہیں لیتی تاہم، منتخب کردہ فنڈنگ پلیٹ فارم کی اپنی فیس ہو سکتی ہیں", "Youtube": "یوٹیوب", "Youtube Language": "", "Youtube Proxy URL": ""}